<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>ICT Tilburg - Home</title>
    <meta name="description" content="Free, innovative IT support in Tilburg. Digital inclusion for everyone, workshops and personal help.">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Styles and fonts -->
    <link rel="icon" type="image/png" href="favicon.png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/styles.css">

</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-top">
            <div class="container">
                <div class="contact-info" aria-label="Contact information">
                    <a href="mailto:<EMAIL>" aria-label="E-mail ICT Tilburg"><i class="fas fa-envelope"></i> <EMAIL></a>
                    <a href="tel:+31612345678" aria-label="Call ICT Tilburg"><i class="fas fa-phone"></i> +31 6 1234 5678</a>
                </div>
                <div class="language-selector" role="navigation" aria-label="Language selection">
                    <button class="lang-btn active" aria-pressed="true" data-lang="en" data-href="index.html">EN</button>
                    <button class="lang-btn" aria-pressed="false" data-lang="nl" data-href="../nl/nl/index.html">NL</button>
                    <button class="lang-btn" aria-pressed="false" data-lang="pt" data-href="../pt/index.html">PT</button>
                </div>
            </div>
        </div>
        <div class="main-header">
            <div class="container">
                <div class="logo-area">
                    <div class="logo" aria-label="Logo">
                        <i class="fas fa-hands-helping"></i>
                    </div>
                    <div class="logo-text">ICT<span>Tilburg</span></div>
                </div>
                <nav aria-label="Main menu">
                    <button class="mobile-menu-btn" aria-label="Open menu" aria-expanded="false">
                        <i class="fas fa-bars"></i>
                    </button>
                    <ul>
                        <li><a href="index.html#home" tabindex="0">Home</a></li>
                        <li><a href="services.html" tabindex="0">Services</a></li>
                        <li><a href="about-us.html" tabindex="0">About Us</a></li>
                        <li><a href="workshops.html" tabindex="0">Workshops</a></li>
                        <li><a href="forum.html" tabindex="0">Forum</a></li>
                        <li><a href="downloads.html" tabindex="0">Downloads</a></li>
                        <li><a href="partners.html" tabindex="0">Partners</a></li>
                        <li><a href="news.html" tabindex="0">News</a></li>
                        <li><a href="blog.html" tabindex="0">Blog</a></li>
                        <li><a href="contact.html" tabindex="0">Contact</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>
    <!-- MAIN CONTENT -->
    <main>
        <section class="section" id="home">
            <div class="container">
                <div class="section-title">
                    <h1>Welcome to ICT Tilburg</h1>
                    <p>Free, innovative IT support for everyone in Tilburg and surrounding areas.</p>
                </div>
                <section class="onboarding-section" aria-label="First time?">
                  <h3>Is this your first time here? Here’s how it works:</h3>
                  <ol>
                    <li>Choose a service or workshop that suits you.</li>
                    <li>Ask your question via the <a href="contact.html">contact form</a> or call us.</li>
                    <li>We will get in touch and plan a solution together.</li>
                    <li>Enjoy free, personal IT support!</li>
                  </ol>
                  <p><b>Tip:</b> Check the <a href="#faq">FAQ</a> below for more answers!</p>
                </section>
                <!-- Example: You can add more info, mission, call-to-action, etc. -->
                <div style="margin: 40px 0;">
                    <h2 style="color:#2c5e92;">Our Mission</h2>
                    <p>
                        We believe technology should be accessible for everyone. Our mission is to break down barriers and promote digital self-reliance – for young and old, individuals or entrepreneurs.
                    </p>
                    <a href="services.html" class="btn" style="display:inline-block;margin-top:15px;background:#43b380;color:#fff;padding:8px 30px;border-radius:18px;font-weight:bold;text-decoration:none;">Discover Our Services</a>
                </div>
            </div>
        </section>
        <section class="faq-section" id="faq" aria-label="Frequently Asked Questions">
          <h2>Frequently Asked Questions (FAQ)</h2>
          <div class="faq-list">
            <details>
              <summary>Who is ICT Tilburg for?</summary>
              <p>For everyone in Tilburg and surroundings who needs digital help: seniors, families, entrepreneurs, associations and the curious.</p>
            </details>
            <details>
              <summary>Are the services really free?</summary>
              <p>Yes, our basic services are free for individuals, non-profits and small organizations. Special projects upon request.</p>
            </details>
            <details>
              <summary>Do I need to know a lot about computers?</summary>
              <p>Not at all! We help both beginners and advanced users, step by step.</p>
            </details>
            <details>
              <summary>How quickly do I get help?</summary>
              <p>We usually respond within 24 hours to your request.</p>
            </details>
            <details>
              <summary>Can I also visit in person?</summary>
              <p>Yes, you can make an appointment for help on location in Tilburg.</p>
            </details>
          </div>
        </section>
    </main>
    <!-- Footer -->
    <footer role="contentinfo">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <div class="logo-area">
                        <div class="logo" aria-label="Logo">
                            <i class="fas fa-hands-helping" aria-hidden="true"></i>
                        </div>
                        <div class="logo-text">ICT<span>Tilburg</span></div>
                    </div>
                    <p>Free, innovative IT support in Tilburg and surroundings.</p>
                    <div class="social-links" aria-label="Social media">
                        <a href="#" tabindex="0" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" tabindex="0" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" tabindex="0" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" tabindex="0" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-col">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html#home">Home</a></li>
                        <li><a href="services.html">Services</a></li>
                        <li><a href="about-us.html">About Us</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                        <li><a href="blog.html">Blog</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Our Services</h3>
                    <ul>
                        <li><a href="remote-support.html">Remote Support</a></li>
                        <li><a href="open-source.html">Open Source</a></li>
                        <li><a href="business-management.html">Business Management</a></li>
                        <li><a href="ai-automation.html">AI & Automation</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Newsletter</h3>
                    <p>Sign up for news, tips, and invitations to free workshops.</p>
                    <form>
                        <div class="form-group">
                            <input type="email" class="form-control" placeholder="Your email" required>
                        </div>
                        <button type="submit" class="btn">Sign Up</button>
                    </form>
                </div>
            </div>
            <div class="copyright">
                <p>&copy; 2025 ICT Tilburg. All rights reserved. | Technology for everyone, community first.</p>
            </div>
        </div>
    </footer>
    <script>
        // Mobile Menu Toggle with ARIA
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const navMenu = document.querySelector('nav ul');
        if (mobileMenuBtn && navMenu) {
            mobileMenuBtn.addEventListener('click', () => {
                navMenu.classList.toggle('show');
                const expanded = mobileMenuBtn.getAttribute('aria-expanded') === 'true';
                mobileMenuBtn.setAttribute('aria-expanded', (!expanded).toString());
            });
        }
        // Language switcher
        const langButtons = document.querySelectorAll('.lang-btn');
        langButtons.forEach(button => {
            button.addEventListener('click', () => {
                langButtons.forEach(btn => btn.classList.remove('active'));
                langButtons.forEach(btn => btn.setAttribute('aria-pressed', 'false'));
                button.classList.add('active');
                button.setAttribute('aria-pressed', 'true');
                
                // Store language preference
                const lang = button.getAttribute('data-lang');
                if (lang) {
                    localStorage.setItem('preferred_language', lang);
                }
                
                // Navigate to the appropriate page
                const href = button.getAttribute('data-href');
                if (href) {
                    window.location.href = href;
                }
            });
        });
    </script>
</body>
</html>