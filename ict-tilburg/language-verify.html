<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Language System Test</title>
    <link rel="stylesheet" href="/assets/css/layout-updates.css">
    <link rel="stylesheet" href="/assets/css/language-transitions.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="/assets/js/language.js"></script>
    <script src="/assets/js/language-transitions.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .content {
            margin-top: 40px;
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            color: #174080;
        }
        .test-section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .header-top {
            background-color: #174080;
            color: white;
            padding: 10px 0;
        }
        .header-top .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .contact-info {
            display: flex;
            gap: 20px;
        }
        .contact-info a {
            color: white;
            text-decoration: none;
        }
        .language-selector {
            display: flex;
            gap: 10px;
        }
        .language-selector a {
            color: white;
            text-decoration: none;
            opacity: 0.8;
        }
        .language-selector a.active,
        .language-selector a:hover {
            opacity: 1;
            font-weight: 600;
            text-decoration: underline;
        }
    </style>
    <script src="/assets/js/language-selector.js"></script>
    <script src="/assets/js/navigation.js"></script>
    <link rel="alternate" hreflang="en" href="/en/language-verify.html">
    <link rel="alternate" hreflang="pt" href="/pt/language-verify.html">
    <link rel="canonical" href="/language-verify.html">
</head>
<body>
    <div class="header-top">
        <div class="container">
            <div class="contact-info">
                <a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> <EMAIL></a>
                <a href="tel:+31612345678"><i class="fas fa-phone"></i> +31 6 1234 5678</a>
            </div>
            <div class="language-selector">
                <a href="/en/language-verify.html" data-lang="en">EN</a>
                <a href="/language-verify.html" data-lang="nl" class="active">NL</a>
                <a href="/pt/language-verify.html" data-lang="pt">PT</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <h1>Taal Systeem Test (NL)</h1>
        <div class="content">
            <div class="test-section">
                <h2>Taalschakelaar Test</h2>
                <p>Hieronder wordt de taalschakelaar weergegeven. Controleer of alle drie talen zichtbaar zijn:</p>
                <div id="language-display"></div>
            </div>
            
            <div class="test-section">
                <h2>Redirectie Test</h2>
                <p>Klik op een van de taallinks hierboven om te controleren of de pagina correct wordt omgeleid.</p>
                <p>Huidige taal: <span id="current-language" class="success">nl</span></p>
            </div>
            
            <div class="test-section">
                <h2>Pop-up Test</h2>
                <p>Er zou GEEN pop-up moeten verschijnen bij het wisselen van talen.</p>
                <div id="popup-result" class="success">Geen pop-up gedetecteerd</div>
            </div>
            
            <a href="/index.html">Terug naar de hoofdpagina</a>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Display language selector content
            const languageSelector = document.querySelector('.language-selector');
            const languageDisplay = document.getElementById('language-display');
            
            if (languageSelector && languageDisplay) {
                const langCount = languageSelector.querySelectorAll('a').length;
                languageDisplay.innerHTML = `
                    <p>Aantal taalopties gevonden: <span class="${langCount >= 3 ? 'success' : 'error'}">${langCount}</span></p>
                    <p>Talen: ${Array.from(languageSelector.querySelectorAll('a')).map(a => a.textContent).join(', ')}</p>
                `;
            }
            
            // Check for current language
            const currentLang = document.getElementById('current-language');
            if (currentLang && typeof languageHandler !== 'undefined') {
                currentLang.textContent = languageHandler.getCurrentLanguage();
            }
        });
    </script>
</body>
</html>
