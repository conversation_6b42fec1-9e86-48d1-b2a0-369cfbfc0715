<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Language Switch Test</title>
    <script>
        // Simplified version of the language handler for testing
        const testLanguageHandler = {
            pageMappings: {
                'over-ons.html': {
                    'en': 'about-us.html',
                    'pt': 'sobre-nos.html'
                },
                'diensten.html': {
                    'en': 'services.html',
                    'pt': 'servicos.html'
                },
                'about-us.html': {
                    'nl': 'over-ons.html',
                    'pt': 'sobre-nos.html'
                },
                'services.html': {
                    'nl': 'diensten.html',
                    'pt': 'servicos.html'
                },
                'sobre-nos.html': {
                    'nl': 'over-ons.html',
                    'en': 'about-us.html'
                },
                'servicos.html': {
                    'nl': 'diensten.html',
                    'en': 'services.html'
                }
            },
            
            mapPage: function(pageName, fromLang, toLang) {
                if (this.pageMappings[pageName] && this.pageMappings[pageName][toLang]) {
                    return this.pageMappings[pageName][toLang];
                }
                return pageName;
            },
            
            testMappings: function() {
                const tests = [
                    { page: 'over-ons.html', from: 'nl', to: 'en', expected: 'about-us.html' },
                    { page: 'diensten.html', from: 'nl', to: 'pt', expected: 'servicos.html' },
                    { page: 'about-us.html', from: 'en', to: 'nl', expected: 'over-ons.html' },
                    { page: 'services.html', from: 'en', to: 'pt', expected: 'servicos.html' },
                    { page: 'sobre-nos.html', from: 'pt', to: 'nl', expected: 'over-ons.html' },
                    { page: 'servicos.html', from: 'pt', to: 'en', expected: 'services.html' }
                ];
                
                let results = '';
                let allPassed = true;
                
                tests.forEach(test => {
                    const actual = this.mapPage(test.page, test.from, test.to);
                    const passed = actual === test.expected;
                    allPassed = allPassed && passed;
                    
                    results += `<tr>
                        <td>${test.page}</td>
                        <td>${test.from} → ${test.to}</td>
                        <td>${test.expected}</td>
                        <td>${actual}</td>
                        <td>${passed ? '✓' : '✗'}</td>
                    </tr>`;
                });
                
                document.getElementById('test-results').innerHTML = results;
                document.getElementById('overall-result').textContent = allPassed ? 'All tests passed!' : 'Some tests failed!';
                document.getElementById('overall-result').className = allPassed ? 'success' : 'error';
            }
        };
        
        window.onload = function() {
            testLanguageHandler.testMappings();
        };
    </script>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
    </style>
    <script src="/assets/js/language.js"></script>
    <script src="/assets/js/language-selector.js"></script>
    <script src="/assets/js/language-transitions.js"></script>
    <script src="/assets/js/navigation.js"></script>
    <link rel="alternate" hreflang="en" href="/en/language-test.html">
    <link rel="alternate" hreflang="pt" href="/pt/language-test.html">
    <link rel="canonical" href="/language-test.html">
    <link rel="stylesheet" href="/assets/css/language-transitions.css">
</head>
<body>
    <h1>Language Switching Test</h1>
    
    <h2>Page Mapping Tests</h2>
    <table>
        <thead>
            <tr>
                <th>Page Name</th>
                <th>Language Conversion</th>
                <th>Expected Result</th>
                <th>Actual Result</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody id="test-results">
            <!-- Results will be populated by JavaScript -->
        </tbody>
    </table>
    
    <h2>Overall Result</h2>
    <p id="overall-result"></p>
    
    <h2>Next Steps</h2>
    <p>To test the full language switching functionality in a real environment:</p>
    <ol>
        <li>Apply the navigation templates using the update scripts</li>
        <li>Open the website in a browser</li>
        <li>Click the language selector links</li>
        <li>Verify you're redirected to the correct language version of the current page</li>
    </ol>
</body>
</html>
