<script>
// <PERSON><PERSON>t to adjust heights and ensure visual harmony
document.addEventListener('DOMContentLoaded', function() {
    // Function to balance heights in the footer columns
    function balanceFooterColumns() {
        const footerColumns = document.querySelectorAll('.footer-col');
        if (window.innerWidth > 768 && footerColumns.length > 1) {
            // Reset heights first
            footerColumns.forEach(col => col.style.height = 'auto');
            
            // Wait for layout to settle
            setTimeout(() => {
                // Get the tallest column
                let maxHeight = 0;
                footerColumns.forEach(col => {
                    maxHeight = Math.max(maxHeight, col.offsetHeight);
                });
                
                // Set a reasonable max height (not too tall)
                maxHeight = Math.min(maxHeight, 220);
                
                // Apply balanced height
                footerColumns.forEach(col => {
                    col.style.height = maxHeight + 'px';
                    col.style.overflow = 'auto';
                });
            }, 100);
        } else {
            // Reset on mobile
            footerColumns.forEach(col => {
                col.style.height = 'auto';
                col.style.overflow = 'visible';
            });
        }
    }
    
    // Run on load and resize
    balanceFooterColumns();
    window.addEventListener('resize', balanceFooterColumns);
});
</script>
