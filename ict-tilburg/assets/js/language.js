// Enhanced language handling with improved page mapping and seamless transitions
const languageHandler = {
    // Language file mappings for pages that have different names in different languages
    pageMappings: {
        // Dutch to English mappings
        'over-ons.html': {
            'en': 'about-us.html',
            'pt': 'sobre-nos.html'
        },
        'diensten.html': {
            'en': 'services.html',
            'pt': 'servicos.html'
        },
        'contact.html': {
            'en': 'contact.html',
            'pt': 'contacto.html'
        },
        'partners.html': {
            'en': 'partners.html',
            'pt': 'parceiros.html'
        },
        'index.html': {
            'en': 'index.html',
            'pt': 'index.html'
        },
        'privacy.html': {
            'en': 'privacy.html',
            'pt': 'politica-de-privacidade.html'
        },
        'terms-gebruik.html': {
            'en': 'terms-of-use.html',
            'pt': 'termos-de-uso.html'
        },
        'terms-diensten.html': {
            'en': 'terms-of-service.html',
            'pt': 'termos-de-servico.html'
        },
        // Added missing mappings for common pages
        'workshops.html': {
            'en': 'workshops.html',
            'pt': 'workshops.html'
        },
        'blog.html': {
            'en': 'blog.html',
            'pt': 'blog.html'
        },
        'nieuws.html': { // NL nieuws.html
            'en': 'news.html' // EN news.html (PT version seems missing from file tree)
            // 'pt': 'noticias.html' // Or blog.html if merged
        },
        'downloads.html': {
            'en': 'downloads.html',
            'pt': 'downloads.html'
        },
        'forum.html': {
            'en': 'forum.html',
            'pt': 'forum.html'
        },
        // English to Dutch/PT mappings
        'about-us.html': {
            'nl': 'over-ons.html',
            'pt': 'sobre-nos.html'
        },
        'services.html': {
            'nl': 'diensten.html',
            'pt': 'servicos.html'
        },
        // EN to NL/PT for added pages
        'workshops.html': { // Assuming workshops.html is the filename in EN too
            'nl': 'workshops.html',
            'pt': 'workshops.html'
        },
        'blog.html': { // Assuming blog.html is the filename in EN too
            'nl': 'blog.html',
            'pt': 'blog.html'
        },
        'news.html': { // EN news.html
            'nl': 'nieuws.html'
            // 'pt': 'noticias.html' // Or blog.html if merged
        },
        'downloads.html': { // Assuming downloads.html is the filename in EN too
            'nl': 'downloads.html',
            'pt': 'downloads.html'
        },
        'forum.html': { // Assuming forum.html is the filename in EN too
            'nl': 'forum.html',
            'pt': 'forum.html'
        },
        // Portuguese to Dutch/EN mappings
        'sobre-nos.html': {
            'nl': 'over-ons.html',
            'en': 'about-us.html'
        },
        'servicos.html': {
            'nl': 'diensten.html',
            'en': 'services.html'
        },
        'contacto.html': {
            'nl': 'contact.html',
            'en': 'contact.html'
        },
        'parceiros.html': {
            'nl': 'partners.html',
            'en': 'partners.html'
        },
        'politica-de-privacidade.html': {
            'nl': 'privacy.html',
            'en': 'privacy.html'
        },
        'termos-de-uso.html': {
            'nl': 'terms-gebruik.html',
            'en': 'terms-of-use.html'
        },
        'termos-de-servico.html': {
            'nl': 'terms-diensten.html',
            'en': 'terms-of-service.html'
        },
        // PT to NL/EN for added pages
        'workshops.html': { // Assuming workshops.html is the filename in PT too
            'nl': 'workshops.html',
            'en': 'workshops.html'
        },
        'blog.html': { // Assuming blog.html is the filename in PT too
            'nl': 'blog.html',
            'en': 'blog.html'
        },
        // 'noticias.html': { // PT noticias.html (if it existed)
        //     'nl': 'nieuws.html',
        //     'en': 'news.html'
        // },
        'downloads.html': { // Assuming downloads.html is the filename in PT too
            'nl': 'downloads.html',
            'en': 'downloads.html'
        },
        'forum.html': { // Assuming forum.html is the filename in PT too
            'nl': 'forum.html',
            'en': 'forum.html'
        }
    },

    init: function() {
        this.initLanguageSelector();
        this.initBrowserLanguageDetection();
        this.initUrlRouting();
    },

    initLanguageSelector: function() {
        // Handle anchor links for language selection
        const langLinks = document.querySelectorAll('.language-selector a');
        langLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const lang = link.getAttribute('data-lang');
                this.setLanguagePreference(lang);
                this.redirectToLanguageVersion(lang, window.location.pathname);
            });
        });

        // Also handle buttons (for backward compatibility)
        const langButtons = document.querySelectorAll('.language-selector button');
        langButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const lang = button.textContent.toLowerCase();
                this.setLanguagePreference(lang);
                this.redirectToLanguageVersion(lang, window.location.pathname);
            });
        });

        this.markCurrentLanguage();
    },

    initBrowserLanguageDetection: function() {
        if (!this.getPreferredLanguage()) {
            const browserLang = navigator.language.split('-')[0];
            const supportedLangs = ['nl', 'en', 'pt'];
            // Use browser language if supported, otherwise default to Dutch
            const detectedLang = supportedLangs.includes(browserLang) ? browserLang : 'nl';
            this.setLanguagePreference(detectedLang);
        }
    },

    initUrlRouting: function() {
        const currentPath = window.location.pathname;
        const preferredLang = this.getPreferredLanguage(); // Get preferred language

        // If on root/index.html and preferred language is not 'nl', redirect to preferred language's index
        if ((currentPath === '/' || currentPath === '/index.html' || currentPath === '/ict-tilburg/') && preferredLang && preferredLang !== 'nl') {
            this.redirectToLanguageVersion(preferredLang, 'index.html'); // Pass 'index.html' as page name
            return; // Exit after redirection
        }

        // Skip further routing for Dutch index if no other preferred language or already on NL
        if ((currentPath === '/' || currentPath === '/index.html' || currentPath === '/ict-tilburg/') && (!preferredLang || preferredLang === 'nl')) {
            return;
        }
        
        const currentLangInPath = this.getCurrentLanguageFromPath(); // Determine language from path
        // If a preferred language is set and it doesn't match the language in the path (and it's not NL root)
        // or if no language in path but preferred is not NL, then redirect.
        if (preferredLang && preferredLang !== currentLangInPath && !((currentLangInPath === 'nl' || !currentLangInPath) && currentPath.startsWith('/')) ) {
            this.redirectToLanguageVersion(preferredLang, currentPath);
        } else if (!currentLangInPath && preferredLang && preferredLang !== 'nl') {
            // Handles cases where URL might be like /about-us.html but preferred is 'en'
            this.redirectToLanguageVersion(preferredLang, currentPath);
        }
    },

    markCurrentLanguage: function() {
        const currentLang = this.getCurrentLanguage();
        
        // Handle anchor links
        const currentLangLink = document.querySelector(`.language-selector a[data-lang="${currentLang}"]`);
        if (currentLangLink) {
            // Remove active class from all language links first
            document.querySelectorAll('.language-selector a').forEach(link => {
                link.classList.remove('active');
            });
            // Add active class to current language link
            currentLangLink.classList.add('active');
        }
        
        // Handle buttons (for backward compatibility)
        const buttons = document.querySelectorAll('.language-selector button');
        if (buttons.length > 0) {
            buttons.forEach(button => {
                const buttonLang = button.textContent.toLowerCase();
                if (buttonLang === currentLang) {
                    button.classList.add('active');
                } else {
                    button.classList.remove('active');
                }
            });
        }
    },

    setLanguagePreference: function(lang) {
        localStorage.setItem('preferred_language', lang);
    },

    getPreferredLanguage: function() {
        return localStorage.getItem('preferred_language');
    },

    getCurrentLanguageFromPath: function() {
        const path = window.location.pathname;
        if (path.includes('/en/') || path.startsWith('/en/')) {
            return 'en';
        } else if (path.includes('/pt/') || path.startsWith('/pt/')) {
            return 'pt';
        } else if (path === '/' || path === '/index.html' || path.startsWith('/ict-tilburg/index.html') || (!path.substring(1).includes('/') && path.endsWith('.html'))) {
             // If it's root, or an HTML file directly in root (e.g. /contact.html), assume 'nl'
            return 'nl';
        }
        return null; // No language indicator in path, or ambiguous
    },

    getCurrentLanguage: function() {
        // Priority: 1. Language from path, 2. Preferred language, 3. Default 'nl'
        const langFromPath = this.getCurrentLanguageFromPath();
        if (langFromPath) return langFromPath;
        return this.getPreferredLanguage() || 'nl';
    },

    mapPage: function(pageName, fromLang, toLang) {
        // Direct match if the page has a mapping
        if (this.pageMappings[pageName] && this.pageMappings[pageName][toLang]) {
            return this.pageMappings[pageName][toLang];
        }
        
        // Default: use the same page name
        return pageName;
    },

    redirectToLanguageVersion: function(lang, path) {
        // Extract the filename from the path
        let filename = path.split('/').pop() || 'index.html';
        if (!filename.includes('.')) {
            filename = 'index.html';
        }
        
        // Map page name if needed
        const currentLang = this.getCurrentLanguage();
        if (this.pageMappings[filename] && this.pageMappings[filename][lang]) {
            filename = this.pageMappings[filename][lang];
        }
        
        // Determine if we're in a subdirectory (en/ or pt/)
        const isInSubdir = path.includes('/en/') || path.includes('/pt/');
        const basePath = isInSubdir ? '../' : '';
        
        // Build the new URL based on the target language
        let newPath;
        // Construct absolute paths for clarity and robustness
        if (lang === 'nl') {
            newPath = '/' + filename;
            // Special case for GitHub pages like environment where root might be /projectname/
            if (window.location.pathname.startsWith('/ict-tilburg/')) {
                newPath = '/ict-tilburg/' + filename;
            }
        } else {
            newPath = '/' + lang + '/' + filename;
            if (window.location.pathname.startsWith('/ict-tilburg/')) {
                newPath = '/ict-tilburg/' + lang + '/' + filename;
            }
        }
        // Ensure no double slashes, except for protocol
        newPath = newPath.replace(/\/\/+/g, '/').replace(':/', '://');
        
        // Redirect only if we're changing language
        if (currentLang !== lang) {
            // Prevent flash by adding a smooth transition
            document.body.style.opacity = '0.8';
            document.body.style.transition = 'opacity 0.2s ease';
            
            // Use timeout to allow the fade to happen before redirecting
            setTimeout(function() {
                window.location.href = newPath;
            }, 100);
        }
    }
};

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
    // Initialize language handler without any debug messages or popups
    try {
        // Ensure all language options are visible in the selector
        const langSelector = document.querySelector('.language-selector');
        if (langSelector) {
            // Check if we need to ensure all 3 languages are present
            const langLinks = langSelector.querySelectorAll('a');
            if (langLinks.length < 3) {
                // Get current page filename
                const path = window.location.pathname;
                const parts = path.split('/').filter(p => p);
                const filename = parts.length > 0 ? parts[parts.length - 1] : 'index.html';
                const currentLang = languageHandler.getCurrentLanguage();
                
                // Create missing language links
                const langData = [
                    {code: 'en', text: 'EN', path: '/en/'},
                    {code: 'nl', text: 'NL', path: '/'},
                    {code: 'pt', text: 'PT', path: '/pt/'}
                ];
                
                // Clear existing links and add all three languages
                langSelector.innerHTML = '';
                langData.forEach(lang => {
                    const a = document.createElement('a');
                    a.href = lang.path + filename;
                    a.setAttribute('data-lang', lang.code);
                    a.textContent = lang.text;
                    if (currentLang === lang.code) {
                        a.classList.add('active');
                    }
                    langSelector.appendChild(a);
                });
            }
        }
        
        // Initialize the language handler
        languageHandler.init();
    } catch (e) {
        // Silent error handling - no popups
    }
});
