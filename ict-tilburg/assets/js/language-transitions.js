// Enhanced language transitions
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth transitions when switching languages
    const languageLinks = document.querySelectorAll('.language-selector a, .language-selector button');
    
    languageLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // If this is already the active language, don't do anything
            if (this.classList.contains('active')) {
                return;
            }
            
            // Get the language from the link
            const targetLang = this.getAttribute('data-lang') || this.textContent.toLowerCase();
            
            // Start transition effect
            document.body.style.opacity = '0.5';
            
            // Mark the content for transition when page loads
            localStorage.setItem('lang_transition', 'true');
            
            // Continue with normal link behavior after a slight delay
            setTimeout(() => {
                // If we have a language.js handler let it handle the transition
                if (window.languageHandler) {
                    // Use the handler
                } else {
                    // Fallback - just navigate to the link
                    window.location.href = this.href;
                }
            }, 300);
        });
    });
    
    // Check if we need to animate in this page
    if (localStorage.getItem('lang_transition') === 'true') {
        // Clear the flag
        localStorage.removeItem('lang_transition');
        
        // Start with low opacity
        document.body.style.opacity = '0';
        document.body.setAttribute('data-lang-transition', 'true');
        
        // Then fade in
        setTimeout(() => {
            document.body.style.opacity = '1';
        }, 50);
    }
});
