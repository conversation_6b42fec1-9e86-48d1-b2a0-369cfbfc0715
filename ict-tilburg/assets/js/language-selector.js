// Language selector helper script
document.addEventListener('DOMContentLoaded', function() {
    // Clean up duplicate language selectors and ensure only one exists
    const languageSelectors = document.querySelectorAll('.language-selector');
    
    // If no selector exists, create one
    if (languageSelectors.length === 0) {
        console.log('No language selector found, creating one...');
        createLanguageSelector();
    }
    // If multiple selectors exist, keep only the first one
    else if (languageSelectors.length > 1) {
        console.log('Multiple language selectors found, cleaning up...');
        // Keep the first one in the header-top section if it exists
        const headerTopSelector = document.querySelector('.header-top .language-selector');
        
        languageSelectors.forEach((selector, index) => {
            // If this is the preferred location or the first one if no preferred location exists
            if ((headerTopSelector && selector === headerTopSelector) || 
                (!headerTopSelector && index === 0)) {
                // Keep this one and ensure it has the correct links
                updateLanguageSelectorLinks(selector);
            } else {
                // Remove duplicate selectors
                selector.remove();
            }
        });
    } else {
        // Just one selector exists, ensure it has the right links
        updateLanguageSelectorLinks(languageSelectors[0]);
    }
});

function updateLanguageSelectorLinks(selector) {
    // Get current page filename
    const path = window.location.pathname;
    const filename = path.split('/').pop() || 'index.html';
    
    // Determine current language from path
    let currentLang = 'nl';
    if (path.includes('/en/')) {
        currentLang = 'en';
    } else if (path.includes('/pt/')) {
        currentLang = 'pt';
    }
    
    // Update links in existing selector
    const enLink = selector.querySelector('[data-lang="en"]');
    const nlLink = selector.querySelector('[data-lang="nl"]');
    const ptLink = selector.querySelector('[data-lang="pt"]');
    
    // First, determine the current directory level
    let basePath = "";
    if (path.includes("/en/") || path.includes("/pt/")) {
        basePath = "../";  // We're in a subdirectory, need to go up one level
    }
    
    if (enLink) {
        enLink.href = currentLang === 'en' ? '#' : basePath + 'en/' + filename;
        enLink.className = currentLang === 'en' ? 'active' : '';
    }
    
    if (nlLink) {
        nlLink.href = currentLang === 'nl' ? '#' : basePath + filename;
        nlLink.className = currentLang === 'nl' ? 'active' : '';
    }
    
    if (ptLink) {
        ptLink.href = currentLang === 'pt' ? '#' : basePath + 'pt/' + filename;
        ptLink.className = currentLang === 'pt' ? 'active' : '';
    }
}

function createLanguageSelector() {
    // Get current page filename
    const path = window.location.pathname;
    const filename = path.split('/').pop() || 'index.html';
    
    // Determine current language from path
    let currentLang = 'nl';
    if (path.includes('/en/')) {
        currentLang = 'en';
    } else if (path.includes('/pt/')) {
        currentLang = 'pt';
    }
    
    // Determine if we're in a subdirectory
    let basePath = "";
    if (path.includes("/en/") || path.includes("/pt/")) {
        basePath = "../";  // We're in a subdirectory, need to go up one level
    }
    
    // Create language selector HTML
    const selectorHtml = `
        <div class="language-selector">
            <a href="${currentLang === 'en' ? '#' : basePath + 'en/' + filename}" 
               data-lang="en" 
               class="${currentLang === 'en' ? 'active' : ''}">EN</a>
            <a href="${currentLang === 'nl' ? '#' : basePath + filename}" 
               data-lang="nl" 
               class="${currentLang === 'nl' ? 'active' : ''}">NL</a>
            <a href="${currentLang === 'pt' ? '#' : basePath + 'pt/' + filename}" 
               data-lang="pt" 
               class="${currentLang === 'pt' ? 'active' : ''}">PT</a>
        </div>
    `;
    
    // Find header area or create one
    const headerTop = document.querySelector('.header-top .container');
    if (headerTop) {
        if (headerTop.querySelector('.contact-info')) {
            headerTop.querySelector('.contact-info').insertAdjacentHTML('afterend', selectorHtml);
        } else {
            headerTop.innerHTML += selectorHtml;
        }
    } else {
        // Create fallback if header structure is different
        const header = document.querySelector('header') || document.body;
        const div = document.createElement('div');
        div.className = 'header-top';
        div.innerHTML = `<div class="container">${selectorHtml}</div>`;
        header.prepend(div);
    }
    console.log('Language selector created');
}
