// Enhanced navigation with search and accessibility features
document.addEventListener('DOMContentLoaded', function() {
    initMobileMenu();
    initActiveNavigation();
    initLanguageSelector();
    initSearch();
    initKeyboardNavigation();
    initBreadcrumbs();
});

function initMobileMenu() {
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navLinks = document.querySelector('.nav-links');

    if (mobileMenuBtn && navLinks) {
        mobileMenuBtn.addEventListener('click', function() {
            toggleMobileMenu(navLinks, this);
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!navLinks.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
                closeMobileMenu(navLinks, mobileMenuBtn);
            }
        });
    }
}

function toggleMobileMenu(navLinks, button) {
    navLinks.classList.toggle('active');
    const icon = button.querySelector('i');
    if (icon) {
        icon.classList.toggle('fa-bars');
        icon.classList.toggle('fa-times');
    }
}

function closeMobileMenu(navLinks, button) {
    if (navLinks.classList.contains('active')) {
        navLinks.classList.remove('active');
        const icon = button.querySelector('i');
        if (icon) {
            icon.classList.add('fa-bars');
            icon.classList.remove('fa-times');
        }
    }
}

function initActiveNavigation() {
    const currentPath = window.location.pathname;
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        const itemPath = item.getAttribute('href');
        if (itemPath === currentPath || 
            (currentPath === '/' && itemPath === '/index.html')) {
            item.classList.add('active');
            item.setAttribute('aria-current', 'page');
        }
    });
}

function initSearch() {
    const searchContainer = document.createElement('div');
    searchContainer.className = 'search-container';
    searchContainer.innerHTML = `
        <input type="search" id="site-search" 
               placeholder="Search..." 
               aria-label="Search through site content">
        <div id="search-results" class="search-results" hidden></div>
    `;

    document.querySelector('nav').appendChild(searchContainer);

    const searchInput = document.getElementById('site-search');
    const searchResults = document.getElementById('search-results');

    searchInput.addEventListener('input', debounce(async (e) => {
        const query = e.target.value.trim();
        if (query.length < 2) {
            searchResults.hidden = true;
            return;
        }

        try {
            const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
            const results = await response.json();
            displaySearchResults(results, searchResults);
        } catch (error) {
            console.error('Search error:', error);
        }
    }, 300));
}

function displaySearchResults(results, container) {
    if (!results.length) {
        container.innerHTML = '<p>No results found</p>';
    } else {
        container.innerHTML = results
            .map(result => `
                <a href="${result.url}" class="search-result">
                    <h3>${result.title}</h3>
                    <p>${result.excerpt}</p>
                </a>
            `)
            .join('');
    }
    container.hidden = false;
}

function initKeyboardNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach((item, index) => {
        item.setAttribute('tabindex', '0');
        item.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowRight':
                    e.preventDefault();
                    navItems[(index + 1) % navItems.length].focus();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    navItems[(index - 1 + navItems.length) % navItems.length].focus();
                    break;
                case 'Enter':
                case ' ':
                    e.preventDefault();
                    item.click();
                    break;
            }
        });
    });
}

function initBreadcrumbs() {
    const currentPath = window.location.pathname;
    const pathParts = currentPath.split('/').filter(Boolean);
    
    if (pathParts.length > 0) {
        const breadcrumbsContainer = document.createElement('div');
        breadcrumbsContainer.className = 'breadcrumbs';
        breadcrumbsContainer.setAttribute('aria-label', 'Breadcrumb');
        
        let breadcrumbsHTML = '<a href="/">Home</a>';
        let currentPath = '';
        
        pathParts.forEach((part, index) => {
            currentPath += `/${part}`;
            const isLast = index === pathParts.length - 1;
            const partName = part.replace(/-/g, ' ').replace('.html', '');
            
            breadcrumbsHTML += ` > ${isLast ? 
                `<span aria-current="page">${partName}</span>` : 
                `<a href="${currentPath}">${partName}</a>`}`;
        });
        
        breadcrumbsContainer.innerHTML = breadcrumbsHTML;
        document.querySelector('main').insertBefore(breadcrumbsContainer, document.querySelector('main').firstChild);
    }
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

    // Language selector
    const langSelector = document.querySelector('.language-selector');
    if (langSelector) {
        langSelector.querySelectorAll('a').forEach(link => {
            link.addEventListener('click', function(e) {
                const lang = this.getAttribute('data-lang');
                localStorage.setItem('preferred_language', lang);
            });
        });

        // Set active language
        const currentLang = document.documentElement.lang;
        const langLink = langSelector.querySelector(`[data-lang="${currentLang}"]`);
        if (langLink) {
            langLink.classList.add('active');
        }
    }
});
