/* Layout updates - reducing header by 25% and footer by 50% */

/* Header size reduction - 25% */
.header-top {
    padding: 6px 0; /* Reduced from 8px */
    font-size: 0.9rem; /* Reduced from 1rem */
}

.header-top .container {
    min-height: 22px; /* Set a minimum height */
}

.main-header {
    padding: 11px 0; /* Reduced from 15px */
}

.main-header .container {
    display: flex;
    align-items: center;
}

.logo {
    width: 45px; /* Reduced from 60px */
    height: 45px; /* Reduced from 60px */
    font-size: 1.5rem; /* Reduced from 2rem */
}

.logo-text {
    font-size: 1.5rem; /* Reduced from 2rem */
}

nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

nav ul {
    gap: 20px; /* Reduced from 25px */
    align-items: center;
}

nav a {
    font-size: 1rem; /* Reduced from 1.15rem */
    padding: 5px 0; /* Add padding for better touch targets */
}

/* Adjust language selector */
.language-selector {
    display: flex;
    align-items: center;
    height: 26px; /* Fixed height to align properly */
}

/* Footer size reduction - 50% */
footer {
    padding: 30px 0 15px; /* Reduced from 60px 0 30px */
}

.footer-grid {
    gap: 20px; /* Reduced from 40px */
    margin-bottom: 20px; /* Reduced from 40px */
    align-items: start; /* Align items to the top */
}

.footer-col {
    padding: 0 5px; /* Add some horizontal padding */
}

.footer-col h3 {
    font-size: 1.1rem; /* Reduced from 1.3rem */
    margin-bottom: 10px; /* Reduced from 20px */
    padding-bottom: 5px; /* Reduced from 10px */
}

.footer-col h3::after {
    height: 1px; /* Reduced from 2px */
    width: 40px; /* Reduced from 50px */
}

.footer-col ul {
    padding: 0;
    margin: 0 0 10px 0;
}

.footer-col ul li {
    margin-bottom: 5px; /* Reduced from 10px */
    font-size: 0.9rem;
    line-height: 1.4;
}

.footer-col p {
    font-size: 0.9rem;
    margin-bottom: 10px;
    line-height: 1.4;
}

.social-links {
    gap: 10px; /* Reduced from 15px */
    margin-top: 10px; /* Reduced from 20px */
    display: flex;
    flex-wrap: wrap;
}

.social-links a {
    width: 30px; /* Reduced from 40px */
    height: 30px; /* Reduced from 40px */
    font-size: 0.9rem; /* Reduced font size */
}

.copyright {
    padding-top: 15px; /* Reduced from 30px */
    font-size: 0.8rem; /* Reduced from 0.9rem */
    margin-top: 5px;
}

/* Form elements in footer */
.footer-col .form-group {
    margin-bottom: 10px;
}

.footer-col .form-control {
    padding: 8px 10px; /* Reduced padding */
    font-size: 0.9rem;
    height: 32px;
}

.footer-col .btn {
    padding: 6px 15px; /* Reduced padding */
    font-size: 0.9rem;
    height: 32px;
}

/* Maintaining visual harmony */
@media (max-width: 768px) {
    .header-top {
        padding: 5px 0;
    }
    
    .contact-info {
        gap: 12px;
    }
    
    .mobile-menu-btn {
        font-size: 1.4rem; /* Reduced from 1.7rem */
    }
    
    .logo {
        width: 40px;
        height: 40px;
    }
    
    .logo-text {
        font-size: 1.3rem;
    }
    
    .footer-grid {
        gap: 15px;
    }
    
    .footer-col {
        padding: 0 10px 15px;
    }
    
    .footer-col h3 {
        margin-bottom: 8px;
    }
    
    /* Fix navigation on mobile */
    nav ul {
        padding: 5px 0;
    }
    
    nav ul li {
        padding: 5px 15px;
    }
    
    nav ul.show {
        box-shadow: 0 5px 10px rgba(0,0,0,0.1);
    }
}

/* Additional adjustments for better spacing and layout */
.section {
    padding-top: 50px; /* Reduced from 70px */
    padding-bottom: 30px; /* Reduced from 40-50px */
}

.section-title {
    margin-bottom: 35px; /* Reduced from 48px */
}

/* Button size adjustments */
.btn {
    padding: 10px 25px; /* Slightly reduced */
    font-size: 1.05rem; /* Slightly reduced */
}

/* English pages specific adjustments */
html[lang="en"] .footer-col h3 {
    font-size: 1.1rem;
    margin-bottom: 10px;
}

html[lang="en"] .footer-col ul li {
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Form and input adjustments */
input, textarea, select, button {
    font-size: 0.95rem;
}

/* Hero section adjustments */
.hero {
    padding: 55px 0 40px 0; /* Reduced from 70px 0 50px 0 */
}

.hero h1 {
    font-size: 2.4rem; /* Reduced from 2.7rem */
    margin-bottom: 15px; /* Reduced from 18px */
}

/* Harmonize spacing throughout the site */
.section > .container {
    padding-top: 10px;
    padding-bottom: 10px;
}

/* Fix any overflow issues */
body {
    overflow-x: hidden;
}

/* Additional space fix for navigation */
.main-header + section, .main-header + main {
    margin-top: -5px; /* Negative margin to compensate for header reduction */
}
