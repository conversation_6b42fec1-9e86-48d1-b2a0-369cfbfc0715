/* Language transition styles */
body {
    transition: opacity 0.2s ease;
}

/* Enhanced language selector styling */
.language-selector {
    display: flex;
    gap: 12px;
    align-items: center;
}

.language-selector a,
.language-selector button {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 0.9rem;
    opacity: 0.75;
    padding: 4px 8px;
    border-radius: 3px;
    transition: all 0.3s ease;
    text-decoration: none;
    position: relative;
    font-weight: 500;
}

.language-selector a:hover,
.language-selector button:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.1);
}

.language-selector a.active,
.language-selector button.active {
    opacity: 1;
    font-weight: 600;
    background-color: rgba(255, 255, 255, 0.2);
}

.language-selector a.active::after,
.language-selector button.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    background-color: var(--secondary, #43b380);
    text-decoration: none;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .language-selector {
        gap: 4px;
    }
    
    .language-selector a,
    .language-selector button {
        padding: 3px 6px;
        font-size: 0.85rem;
    }
}
