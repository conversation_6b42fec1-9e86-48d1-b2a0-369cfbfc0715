/* Common styles for ICT Tilburg website */

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
}

/* Common layout components */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header styles */
header {
    background-color: #ffffff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    padding: 1rem 0;
}

/* Navigation styles */
nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Enhanced Navigation Styles */
.header-top {
    background-color: var(--primary);
    color: white;
    padding: 0.5rem 0;
    font-size: 0.9rem;
}

.header-top .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.contact-info {
    display: flex;
    gap: 1.5rem;
}

.contact-info a {
    color: white;
    text-decoration: none;
    transition: opacity 0.3s;
}

.contact-info a:hover {
    opacity: 0.8;
}

.language-selector {
    display: flex;
    gap: 1rem;
}

.language-selector a {
    color: white;
    text-decoration: none;
    opacity: 0.7;
    transition: opacity 0.3s;
    padding: 0.2rem 0.5rem;
}

.language-selector a.active {
    opacity: 1;
    font-weight: 600;
    border-bottom: 2px solid var(--accent);
}

.main-header {
    background: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 1.5rem;
    margin: 0;
    padding: 0;
}

.nav-item {
    color: var(--dark);
    text-decoration: none;
    font-weight: 500;
    position: relative;
    padding: 0.5rem 0;
}

.nav-item:hover,
.nav-item.active {
    color: var(--primary);
}

.nav-item::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary);
    transition: width 0.3s ease;
}

.nav-item:hover::after,
.nav-item.active::after {
    width: 100%;
}

/* Mobile Navigation */
.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--primary);
}

@media (max-width: 1024px) {
    .nav-links {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        flex-direction: column;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .nav-links.active {
        display: flex;
    }

    .mobile-menu-btn {
        display: block;
    }

    .contact-info {
        display: none;
    }

    .language-selector {
        width: 100%;
        justify-content: center;
    }
}

/* Footer styles */
footer {
    background-color: #333;
    color: #fff;
    padding: 2rem 0;
    margin-top: 3rem;
}

/* Common components */
.button {
    display: inline-block;
    padding: 10px 20px;
    border-radius: 5px;
    text-decoration: none;
    transition: background-color 0.3s;
}

/* Blog specific styles */
.blog-hero {
    background: linear-gradient(rgba(44, 94, 146, 0.80), rgba(44, 94, 146, 0.85)), url('../images/blog-hero-bg.jpg') center/cover no-repeat;
    color: white;
    padding: 70px 0 50px 0;
    text-align: center;
    margin-bottom: 30px;
}

.blog-hero h1 { font-size: 2.4rem; margin-bottom: 14px; }
.blog-hero p { font-size: 1.3rem; max-width: 700px; margin: 0 auto; }

.blog-main { 
    display: grid; 
    grid-template-columns: 2fr 1fr; 
    gap: 50px; 
}

.blog-write-area {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    padding: 30px;
    margin-bottom: 35px;
}

.blog-articles-list { margin-bottom: 35px; }

.blog-article {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.04);
    padding: 28px;
    margin-bottom: 28px;
    border-left: 5px solid var(--secondary);
}

/* Page-specific styles */

/* Hero section */
.hero {
    background: linear-gradient(rgba(44, 94, 146, 0.85), rgba(44, 94, 146, 0.90)), url('../images/hero-bg.jpg') center/cover;
    color: white;
    padding: 100px 0;
    text-align: center;
}

.hero h1 {
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

/* Services section */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.service-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
}

/* News section */
.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.news-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Contact form */
.contact-form {
    max-width: 600px;
    margin: 2rem auto;
    padding: 2rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

/* Downloads section */
.downloads-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.download-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
}

/* Partners section */
.partners-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.partner-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
}

/* Responsive design for blog */
@media (max-width: 1000px) {
    .blog-main { 
        grid-template-columns: 1fr; 
    }
}
