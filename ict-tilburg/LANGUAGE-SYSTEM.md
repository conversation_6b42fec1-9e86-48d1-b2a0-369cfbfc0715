# ICT Tilburg Multi-Language System

This document explains how the multi-language system works for the ICT Tilburg website.

## Languages Supported

The website supports three languages:
- Dutch (NL) - Default language, files stored in the root directory
- English (EN) - Files stored in the `/en/` directory
- Portuguese (PT) - Files stored in the `/pt/` directory

## How the Language System Works

The language system consists of several components:

1. **Language Detection**:
   - Detects the user's browser language preference
   - Remembers user language preference in localStorage
   - Redirects users to their preferred language version

2. **Navigation Templates**:
   - Each language has its own navigation template:
     - `assets/templates/standard-nav.html` - Dutch (NL)
     - `assets/templates/en-nav.html` - English (EN)
     - `assets/templates/pt-nav.html` - Portuguese (PT)

3. **Language Switcher**:
   - Located in the top header of each page
   - Allows users to manually switch between languages
   - Automatically highlights the current language

4. **Page Mapping**:
   - Maps between equivalent pages across languages
   - Handles different filenames (e.g., "over-ons.html" to "about-us.html")

## Maintaining the Multi-Language System

### Adding a New Page

When adding a new page, follow these steps:

1. Create the page in all three languages:
   - Dutch version in the root directory (e.g., `nieuwe-pagina.html`)
   - English version in the `/en/` directory (e.g., `new-page.html`)
   - Portuguese version in the `/pt/` directory (e.g., `nova-pagina.html`)

2. Add the page mapping to `assets/js/language.js`:
   ```javascript
   'nieuwe-pagina.html': {
       'en': 'new-page.html',
       'pt': 'nova-pagina.html'
   },
   'new-page.html': {
       'nl': 'nieuwe-pagina.html',
       'pt': 'nova-pagina.html'
   },
   'nova-pagina.html': {
       'nl': 'nieuwe-pagina.html',
       'en': 'new-page.html'
   }
   ```

3. Update the navigation in all language templates if the page should appear in the navigation menu.

4. Run the update scripts to apply the changes:
   ```bash
   ./update-all-languages.sh
   ./update-page-references.sh
   ```

### Updating Existing Pages

After making changes to page content:

1. Ensure consistent translations across all language versions.
2. Run `./update-all-languages.sh` to update the navigation for all language versions.
3. Run `./update-page-references.sh` to ensure all internal links are correct.

### Scripts Available

- `update-nl-navigation.sh` - Updates navigation for Dutch pages
- `update-en-navigation.sh` - Updates navigation for English pages
- `update-pt-navigation.sh` - Updates navigation for Portuguese pages
- `update-all-languages.sh` - Updates all language navigation in one go
- `update-page-references.sh` - Fixes internal links between pages

## Technical Implementation

- The language switching logic is in `assets/js/language.js`
- Navigation templates are in `assets/templates/`
- Each HTML page has proper language alternates in the `<head>` section
- Canonical URLs are set for proper SEO

## Troubleshooting

- **Language not switching**: Check localStorage settings in the browser
- **Wrong navigation language**: Run update scripts again
- **Broken links**: Check URL references and run `update-page-references.sh`
- **Missing translations**: Update templates and content files

## Future Improvements

Potential improvements to consider:
1. Implement a content management system (CMS)
2. Use a shared template system with content variables
3. Consider a framework with built-in i18n support
