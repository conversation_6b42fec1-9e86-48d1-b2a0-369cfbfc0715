const express = require('express');
const { PrismaClient } = require('@prisma/client');
const cors = require('cors');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

const app = express();
const prisma = new PrismaClient();

// Security middleware
app.use(helmet());
app.use(cors());
app.use(express.json());

// Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // limit each IP to 100 requests per windowMs
});
app.use(limiter);

// Error handling middleware
const errorHandler = (err, req, res, next) => {
    console.error(err.stack);
    res.status(err.status || 500).json({
        error: {
            message: err.message || 'Internal server error',
            status: err.status || 500
        }
    });
};

// Authentication middleware
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ error: 'Authentication required' });
    }

    jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
        if (err) return res.status(403).json({ error: 'Invalid token' });
        req.user = user;
        next();
    });
};

// Auth routes
app.post('/auth/register', async (req, res, next) => {
    try {
        const { email, password, name } = req.body;
        
        const existingUser = await prisma.user.findUnique({ where: { email } });
        if (existingUser) {
            return res.status(400).json({ error: 'Email already registered' });
        }

        const hashedPassword = await bcrypt.hash(password, 10);
        const user = await prisma.user.create({
            data: { email, password: hashedPassword, name }
        });

        const token = jwt.sign({ userId: user.id }, process.env.JWT_SECRET);
        res.json({ token });
    } catch (error) {
        next(error);
    }
});

app.post('/auth/login', async (req, res, next) => {
    try {
        const { email, password } = req.body;
        const user = await prisma.user.findUnique({ where: { email } });
        
        if (!user || !await bcrypt.compare(password, user.password)) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        const token = jwt.sign({ userId: user.id }, process.env.JWT_SECRET);
        res.json({ token });
    } catch (error) {
        next(error);
    }
});

// Protected routes
app.get('/api/search', authenticateToken, async (req, res, next) => {
    try {
        const { q } = req.query;
        if (!q || q.length < 2) {
            return res.status(400).json({ error: 'Search query too short' });
        }

        const results = await prisma.page.findMany({
            where: {
                OR: [
                    { title: { contains: q, mode: 'insensitive' } },
                    { content: { contains: q, mode: 'insensitive' } }
                ]
            },
            select: {
                title: true,
                url: true,
                excerpt: true
            }
        });

        res.json(results);
    } catch (error) {
        next(error);
    }
});

// CRUD Clientes with authentication
app.get('/clientes', authenticateToken, async (req, res, next) => {
    try {
        const clientes = await prisma.cliente.findMany({
            include: { atividades: true },
            where: { userId: req.user.userId }
        });
        res.json(clientes);
    } catch (error) {
        next(error);
    }
});

app.post('/clientes', authenticateToken, async (req, res, next) => {
    try {
        const { nome, email, telefone } = req.body;
        const cliente = await prisma.cliente.create({
            data: {
                nome,
                email,
                telefone,
                userId: req.user.userId
            }
        });
        res.json(cliente);
    } catch (error) {
        next(error);
    }
});

// CRUD Atividades with authentication
app.get('/atividades', authenticateToken, async (req, res, next) => {
    try {
        const atividades = await prisma.atividade.findMany({
            include: { cliente: true },
            where: { cliente: { userId: req.user.userId } }
        });
        res.json(atividades);
    } catch (error) {
        next(error);
    }
});

app.post('/atividades', authenticateToken, async (req, res, next) => {
    try {
        const { clienteId, tipo, descricao, data, duracao } = req.body;
        
        // Verify client belongs to user
        const cliente = await prisma.cliente.findFirst({
            where: { id: clienteId, userId: req.user.userId }
        });
        
        if (!cliente) {
            return res.status(403).json({ error: 'Unauthorized access to client' });
        }

        const atividade = await prisma.atividade.create({
            data: { clienteId, tipo, descricao, data: new Date(data), duracao }
        });
        res.json(atividade);
    } catch (error) {
        next(error);
    }
});

// Statistics routes with authentication
app.get('/estatisticas/clientes', authenticateToken, async (req, res, next) => {
    try {
        const total = await prisma.cliente.count({
            where: { userId: req.user.userId }
        });
        
        const atividadesCount = await prisma.atividade.count({
            where: { cliente: { userId: req.user.userId } }
        });
        
        const mediaAtividadesPorCliente = total > 0 ? atividadesCount / total : 0;
        
        res.json({
            total_clientes: total,
            total_atividades: atividadesCount,
            media_atividades_por_cliente: mediaAtividadesPorCliente
        });
    } catch (error) {
        next(error);
    }
});

// Error handling
app.use(errorHandler);

// Start server
const port = process.env.PORT || 4000;
app.listen(port, () => console.log(`API running on http://localhost:${port}`));