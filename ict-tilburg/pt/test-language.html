<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Mudança de Idioma (Português)</title>
    <script src="/assets/js/language.js"></script>
    <script src="/assets/js/language-transitions.js"></script>
    <!-- Include console logging to see what's happening -->
    <script>
        // Add debugging code to verify language switching
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Idioma atual detectado:', languageHandler.getCurrentLanguage());
            console.log('Idioma preferido no localStorage:', languageHandler.getPreferredLanguage());
            
            // Add event listeners to log language switching
            document.querySelectorAll('.language-selector a').forEach(link => {
                link.addEventListener('click', (e) => {
                    console.log('Link de idioma clicado:', link.getAttribute('data-lang'));
                });
            });
        });
    </script>
    <script src="/assets/js/navigation.js"></script>
    <link rel="alternate" hreflang="nl" href="/test-language.html">
    <link rel="alternate" hreflang="en" href="/en/test-language.html">
    <link rel="canonical" href="/pt/test-language.html">
    <link rel="stylesheet" href="/assets/css/language-transitions.css">
</head>
<body>
    <h1>Página de Teste de Mudança de Idioma (Português)</h1>
    
    <div class="language-selector">
        <a href="/en/test-language.html" data-lang="en">EN</a>
        <a href="/test-language.html" data-lang="nl">NL</a>
        <a href="/pt/test-language.html" data-lang="pt" class="active">PT</a>
    </div>
    
    <p>Esta página testa a funcionalidade de mudança de idioma.</p>
    <p>Abra o console do navegador para ver informações de depuração.</p>
    
    <h2>Links de Teste</h2>
    <ul>
        <li><a href="javascript:void(0)" onclick="languageHandler.redirectToLanguageVersion('en', window.location.pathname)">Mudar para EN</a></li>
        <li><a href="javascript:void(0)" onclick="languageHandler.redirectToLanguageVersion('nl', window.location.pathname)">Mudar para NL</a></li>
        <li><a href="javascript:void(0)" onclick="languageHandler.redirectToLanguageVersion('pt', window.location.pathname)">Mudar para PT</a></li>
    </ul>
</body>
</html>
