<!DOCTYPE html>
<html lang="pt" lang="pt, nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICT Tilburg - Workshops</title>
    <link rel="stylesheet" href="/assets/css/layout-updates.css">
    <link rel="stylesheet" href="/assets/css/language-transitions.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #2c5e92;
            --secondary: #43b380;
            --accent: #ff6b35;
            --light: #f8f9fa;
            --dark: #343a40;
            --gray: #6c757d;
            --font-main: 'Open Sans', sans-serif;
            --font-heading: 'Roboto', sans-serif;
        }
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: var(--font-main); color: var(--dark); line-height: 1.6; background-color: #f0f4f8; }
        .container { width: 90%; max-width: 1200px; margin: 0 auto; }
        header, footer { background-color: white; }
        .header-top { background-color: var(--primary); color: white; padding: 8px 0; font-size: 0.9rem; }
        .header-top .container { display: flex; justify-content: space-between; align-items: center; }
        .contact-info { display: flex; gap: 20px; }
        .contact-info a { color: white; text-decoration: none; }
        .language-selector { display: flex; gap: 10px; }
        .lang-btn { background: none; border: none; color: white; cursor: pointer; font-size: 0.9rem; opacity: 0.7; }
        .lang-btn.active { opacity: 1; font-weight: 600; text-decoration: underline; }
        .main-header { padding: 15px 0; }
        .logo-area { display: flex; align-items: center; gap: 15px; }
        .logo { width: 60px; height: 60px; background-color: var(--secondary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.8rem; }
        .logo-text { font-family: var(--font-heading); font-weight: 700; font-size: 1.8rem; color: var(--primary); }
        .logo-text span { color: var(--secondary); }
        nav ul { display: flex; list-style: none; gap: 25px; }
        nav a { text-decoration: none; color: var(--dark); font-weight: 600; font-size: 1.1rem; transition: color 0.3s; position: relative; }
        nav a:hover { color: var(--primary); }
        nav a::after { content: ''; position: absolute; bottom: -5px; left: 0; width: 0; height: 2px; background-color: var(--primary); transition: width 0.3s; }
        nav a:hover::after { width: 100%; }
        .mobile-menu-btn { display: none; background: none; border: none; font-size: 1.5rem; cursor: pointer; color: var(--primary); }
        .section { padding: 80px 0; }
        .section-title { text-align: center; margin-bottom: 50px; }
        .section-title h2 { font-size: 2.2rem; color: var(--primary); margin-bottom: 15px; position: relative; display: inline-block; }
        .section-title h2::after { content: ''; position: absolute; bottom: -10px; left: 50%; transform: translateX(-50%); width: 80px; height: 4px; background-color: var(--secondary); }
        .section-title p { color: var(--gray); max-width: 700px; margin: 20px auto 0; }
        .workshops-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); gap: 35px; }
        .workshop-card { background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0,0,0,0.05); transition: transform 0.3s; display: flex; flex-direction: column; }
        .workshop-card:hover { transform: translateY(-10px); }
        .workshop-icon { background-color: var(--primary); color: white; font-size: 2.5rem; height: 120px; display: flex; align-items: center; justify-content: center; }
        .workshop-content { padding: 25px; flex: 1; display: flex; flex-direction: column; }
        .workshop-content h3 { font-size: 1.4rem; margin-bottom: 15px; color: var(--primary); }
        .workshop-content ul { margin-left: 18px; margin-bottom: 15px; }
        .more-link { margin-top: auto; text-align: right; }
        .more-link a { color: var(--accent); font-weight: bold; text-decoration: none; font-size: 1rem; }
        .more-link a:hover { text-decoration: underline; }
        .info-section { max-width: 850px; margin: 0 auto 70px; text-align: center; color: var(--gray);}
        .info-section p { margin-bottom: 20px; }
        footer { background-color: var(--dark); color: white; padding: 60px 0 30px; }
        .footer-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 40px; }
        .footer-col h3 { font-size: 1.3rem; margin-bottom: 20px; position: relative; padding-bottom: 10px; }
        .footer-col h3::after { content: ''; position: absolute; bottom: 0; left: 0; width: 50px; height: 2px; background-color: var(--secondary); }
        .footer-col ul { list-style: none; }
        .footer-col ul li { margin-bottom: 10px; }
        .footer-col a { color: #ddd; text-decoration: none; transition: color 0.3s; }
        .footer-col a:hover { color: var(--secondary); }
        .social-links { display: flex; gap: 15px; margin-top: 20px; }
        .social-links a { display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 50%; background-color: rgba(255,255,255,0.1); color: white; transition: all 0.3s; }
        .social-links a:hover { background-color: var(--secondary); transform: translateY(-3px); }
        .copyright { text-align: center; padding-top: 30px; border-top: 1px solid rgba(255,255,255,0.1); font-size: 0.9rem; color: #aaa; }
        @media (max-width: 992px) {
            .workshops-grid, .footer-grid { grid-template-columns: 1fr; }
        }
    </style>
    <script src="/assets/js/language.js"></script>
    <script src="/assets/js/language-selector.js"></script>
    <script src="/assets/js/language-transitions.js"></script>
    <script src="/assets/js/navigation.js"></script>
    <link rel="alternate" hreflang="nl" href="/workshops.html">
    <link rel="alternate" hreflang="en" href="/en/workshops.html">
    <link rel="canonical" href="/pt/workshops.html">
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-top">
            <div class="container">
                <div class="contact-info">
                    <a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> <EMAIL></a>
                    <a href="tel:+31612345678"><i class="fas fa-phone"></i> +31 6 1234 5678</a>
                </div>
                <div class="language-selector">
                    <button class="lang-btn active">PT</button>
                    <button class="lang-btn">NL</button>
                </div>
            </div>
        </div>
        <div class="main-header">
            <div class="container">
                <div class="logo-area">
                    <div class="logo">
                        <i class="fas fa-hands-helping"></i>
                    </div>
                    <div class="logo-text">ICT<span>Tilburg</span></div>
                </div>
                <nav>
                    <button class="mobile-menu-btn">
                        <i class="fas fa-bars"></i>
                    </button>
                    <ul>
                        <li><a href="index.html#home">Início</a></li>
                        <li><a href="servicos.html">Serviços</a></li>
                        <li><a href="sobre-nos.html">Sobre Nós</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                        <li><a href="index.html#contact">Contacto</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <!-- Workshops Section -->
    <section class="section" id="workshops">
        <div class="container">
            <div class="section-title">
                <h2>Workshops & Formação</h2>
                <p>Descubra, aprenda e cresça connosco. Participe nos nossos workshops gratuitos e torne-se mais autónomo na era digital!</p>
            </div>

            <div class="info-section">
                <p>
                    Organizamos regularmente workshops práticos, presenciais e online, para diferentes níveis de conhecimento. 
                    O objetivo é capacitar a comunidade — seniores, empreendedores, estudantes e curiosos — para usar a tecnologia de forma segura, eficiente e criativa.
                </p>
                <p>
                    Os temas vão desde a iniciação à informática, passando por segurança digital, Linux, inteligência artificial, automação e criação de websites.<br>
                    <b>Todos os eventos são gratuitos e abertos à comunidade de Tilburg e arredores!</b>
                </p>
            </div>

            <div class="workshops-grid">
                <div class="workshop-card">
                    <div class="workshop-icon"><i class="fas fa-shield-alt"></i></div>
                    <div class="workshop-content">
                        <h3>Segurança Digital</h3>
                        <ul>
                            <li>Como proteger-se de fraudes e golpes online</li>
                            <li>Boas práticas de senhas e privacidade</li>
                            <li>Identificação de emails e mensagens suspeitas</li>
                        </ul>
                        <div class="more-link"><a href="workshop-seguranca.html">Mais detalhes &raquo;</a></div>
                    </div>
                </div>
                <div class="workshop-card">
                    <div class="workshop-icon"><i class="fab fa-linux"></i></div>
                    <div class="workshop-content">
                        <h3>Linux e Software Livre</h3>
                        <ul>
                            <li>Instalação e primeiros passos no Linux</li>
                            <li>Aplicações gratuitas para o dia a dia</li>
                            <li>Comunidade open source e suporte</li>
                        </ul>
                        <div class="more-link"><a href="workshop-linux.html">Mais detalhes &raquo;</a></div>
                    </div>
                </div>
                <div class="workshop-card">
                    <div class="workshop-icon"><i class="fas fa-robot"></i></div>
                    <div class="workshop-content">
                        <h3>Inteligência Artificial no Cotidiano</h3>
                        <ul>
                            <li>O que é IA e como pode ajudar no dia a dia</li>
                            <li>Ferramentas práticas (chatbots, assistentes, automação)</li>
                            <li>IA para pequenas empresas e criatividade</li>
                        </ul>
                        <div class="more-link"><a href="workshop-ia.html">Mais detalhes &raquo;</a></div>
                    </div>
                </div>
                <div class="workshop-card">
                    <div class="workshop-icon"><i class="fas fa-globe"></i></div>
                    <div class="workshop-content">
                        <h3>Criação de Websites</h3>
                        <ul>
                            <li>Noções básicas de web e domínios</li>
                            <li>Como criar seu próprio site ou loja virtual</li>
                            <li>Ferramentas gratuitas e dicas de design acessível</li>
                        </ul>
                        <div class="more-link"><a href="workshop-web.html">Mais detalhes &raquo;</a></div>
                    </div>
                </div>
                <div class="workshop-card">
                    <div class="workshop-icon"><i class="fas fa-network-wired"></i></div>
                    <div class="workshop-content">
                        <h3>Automação e Produtividade</h3>
                        <ul>
                            <li>Como automatizar tarefas repetitivas</li>
                            <li>Ferramentas grátis para aumentar a produtividade</li>
                            <li>Integrando aplicações no seu dia a dia</li>
                        </ul>
                        <div class="more-link"><a href="workshop-automacao.html">Mais detalhes &raquo;</a></div>
                    </div>
                </div>
                <div class="workshop-card">
                    <div class="workshop-icon"><i class="fas fa-users"></i></div>
                    <div class="workshop-content">
                        <h3>Workshops Personalizados</h3>
                        <ul>
                            <li>Formação para grupos, escolas e associações</li>
                            <li>Tópicos à medida das necessidades da comunidade</li>
                            <li>Consultoria e acompanhamento individual</li>
                        </ul>
                        <div class="more-link"><a href="workshop-personalizado.html">Mais detalhes &raquo;</a></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <div class="logo-area">
                        <div class="logo">
                            <i class="fas fa-hands-helping"></i>
                        </div>
                        <div class="logo-text">ICT<span>Tilburg</span></div>
                    </div>
                    <p>Suporte informático gratuito, inovador e próximo da comunidade de Tilburg e região.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-col">
                    <h3>Links Rápidos</h3>
                    <ul>
                        <li><a href="index.html#home">Início</a></li>
                        <li><a href="servicos.html">Serviços</a></li>
                        <li><a href="sobre-nos.html">Sobre Nós</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                        <li><a href="index.html#contact">Contacto</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Nossos Serviços</h3>
                    <ul>
                        <li><a href="suporte-remoto.html">Suporte Remoto</a></li>
                        <li><a href="open-source.html">Open Source</a></li>
                        <li><a href="gestao-negocios.html">Gestão para Negócios</a></li>
                        <li><a href="inteligencia-artificial.html">Inteligência Artificial</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Newsletter</h3>
                    <p>Receba novidades, dicas e convites para os nossos workshops gratuitos.</p>
                    <form>
                        <div class="form-group">
                            <input type="email" class="form-control" placeholder="Seu email" required>
                        </div>
                        <button type="submit" class="btn">Subscrever</button>
                    </form>
                </div>
            </div>
            <div class="copyright">
                <p>&copy; 2025 ICT Tilburg. Todos os direitos reservados. | Tecnologia para todos, comunidade em primeiro lugar.</p>
            </div>
        </div>
    </footer>
    <script>
        // Mobile Menu Toggle
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const navMenu = document.querySelector('nav ul');
        if (mobileMenuBtn && navMenu) {
            mobileMenuBtn.addEventListener('click', () => {
                navMenu.classList.toggle('show');
            });
        }
        // Language Switcher
        const langButtons = document.querySelectorAll('.lang-btn');
        langButtons.forEach(button => {
            button.addEventListener('click', () => {
                langButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                alert('Language switched to ' + button.textContent);
            });
        });
    </script>
</body>
</html>