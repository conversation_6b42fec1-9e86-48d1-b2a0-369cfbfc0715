<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Language Switch Test (English)</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .language-selector { display: flex; gap: 10px; margin-bottom: 20px; }
        .language-selector a { padding: 5px 10px; text-decoration: none; color: #333; border: 1px solid #ccc; }
        .language-selector a.active { background-color: #2c5e92; color: white; }
        button { padding: 10px; margin-top: 10px; }
    </style>
    <script src="/assets/js/language.js"></script>
    <script src="/assets/js/language-transitions.js"></script>
    <script src="/assets/js/navigation.js"></script>
    <link rel="alternate" hreflang="nl" href="/simple-language-test.html">
    <link rel="alternate" hreflang="pt" href="/pt/simple-language-test.html">
    <link rel="canonical" href="/en/simple-language-test.html">
    <link rel="stylesheet" href="/assets/css/language-transitions.css">
    <script src="/assets/js/language-selector.js"></script>
</head>
<body>
    <h1>Language Switch Test (English)</h1>
    
    <div class="language-selector">
        <a href="/en/simple-language-test.html" data-lang="en" class="active">EN</a>
        <a href="/simple-language-test.html" data-lang="nl">NL</a>
        <a href="/pt/simple-language-test.html" data-lang="pt">PT</a>
    </div>
    
    <div>
        <p><strong>Current path:</strong> <span id="current-path"></span></p>
        <p><strong>Detected language:</strong> <span id="detected-lang"></span></p>
        <p><strong>Stored preference:</strong> <span id="stored-pref"></span></p>
    </div>
    
    <h2>Manual Language Switch</h2>
    <button onclick="switchToLanguage('en')">Switch to English</button>
    <button onclick="switchToLanguage('nl')">Switch to Dutch</button>
    <button onclick="switchToLanguage('pt')">Switch to Portuguese</button>
    
    <script>
        // Display current info
        document.getElementById('current-path').textContent = window.location.pathname;
        document.getElementById('detected-lang').textContent = languageHandler.getCurrentLanguage();
        document.getElementById('stored-pref').textContent = languageHandler.getPreferredLanguage() || 'none';
        
        // Function to manually switch language
        function switchToLanguage(lang) {
            languageHandler.setLanguagePreference(lang);
            languageHandler.redirectToLanguageVersion(lang, window.location.pathname);
        }
    </script>
</body>
</html>
