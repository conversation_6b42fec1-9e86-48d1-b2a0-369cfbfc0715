<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>ICT Tilburg - Blog & News</title>
    <meta name="description" content="Read and share stories, tips, digital experiences, and latest news from the ICT Tilburg community.">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Styles and fonts -->
    <link rel="icon" type="image/png" href="favicon.png">
    <link rel="stylesheet" href="/assets/css/layout-updates.css">
    <link rel="stylesheet" href="/assets/css/language-transitions.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #2c5e92;
            --secondary: #43b380;
            --accent: #ff6b35;
            --light: #f8f9fa;
            --dark: #212529;
            --gray: #6c757d;
            --font-main: 'Open Sans', sans-serif;
            --font-heading: 'Roboto', sans-serif;
        }
        html { font-size: 18px; }
        body { font-family: var(--font-main); color: var(--dark); background-color: #f0f4f8; }
        .container { width: 95%; max-width: 1200px; margin: 0 auto; }
        header, footer { background-color: white; }
        .header-top { background-color: var(--primary); color: white; padding: 8px 0; font-size: 1rem; }
        .header-top .container { display: flex; justify-content: space-between; align-items: center; }
        .contact-info { display: flex; gap: 20px; }
        .contact-info a { color: white; text-decoration: none; }
        .language-selector { display: flex; gap: 10px; }
        .lang-btn { background: none; border: none; color: white; cursor: pointer; font-size: 1rem; opacity: 0.7; }
        .lang-btn.active, .lang-btn:focus { opacity: 1; font-weight: 600; text-decoration: underline; }
        .main-header { padding: 15px 0; }
        .logo-area { display: flex; align-items: center; gap: 15px; }
        .logo { width: 60px; height: 60px; background-color: var(--secondary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; }
        .logo-text { font-family: var(--font-heading); font-weight: 700; font-size: 2rem; color: var(--primary); }
        .logo-text span { color: var(--secondary); }
        nav ul { display: flex; list-style: none; gap: 25px; }
        nav a { text-decoration: none; color: var(--dark); font-weight: 600; font-size: 1.15rem; transition: color 0.3s; position: relative; }
        nav a:focus { outline: 2px solid var(--secondary); outline-offset: 2px; }
        nav a:hover { color: var(--primary); }
        nav a::after { content: ''; position: absolute; bottom: -5px; left: 0; width: 0; height: 2px; background-color: var(--primary); transition: width 0.3s; }
        nav a:hover::after { width: 100%; }
        .mobile-menu-btn { display: none; background: none; border: none; font-size: 1.7rem; cursor: pointer; color: var(--primary); }
        @media (max-width: 900px) {
            .about-content { flex-direction: column; gap: 35px; }
        }
        @media (max-width: 768px) {
            nav ul { display: none; position: absolute; background: #fff; left: 0; width: 100%; flex-direction: column; gap: 0; top: 100%; z-index: 100; }
            nav ul.show { display: flex; }
            .mobile-menu-btn { display: block; }
        }
        .section { padding: 80px 0 40px 0; }
        .section-title { text-align: center; margin-bottom: 50px; }
        .section-title h2 { font-size: 2rem; color: var(--primary); margin-bottom: 15px; position: relative; display: inline-block; }
        .section-title h2::after { content: ''; position: absolute; bottom: -10px; left: 50%; transform: translateX(-50%); width: 80px; height: 4px; background-color: var(--secondary); }
        .section-title p { color: var(--gray); max-width: 700px; margin: 20px auto 0; }
        .onboarding-section, .faq-section {
            background: #eafbf3;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
        }
        .onboarding-section h3 { margin-top: 0; color: var(--primary);}
        .onboarding-section p { margin: 13px 0 10px 0; }
        .onboarding-section a { color: var(--secondary); font-weight: bold; text-decoration: underline; }
        .faq-section { margin-top: 60px; }
        .faq-section h2 { color: var(--primary);}
        .faq-list details { margin-bottom: 12px; background: #fff; border-radius: 5px; padding: 7px 14px;}
        .faq-list summary { font-weight: 600; cursor: pointer;}
        .faq-list p { margin: 10px 0 5px 0; color: var(--dark);}
        .blog-list { display: grid; grid-template-columns: repeat(auto-fit, minmax(290px, 1fr)); gap: 32px; margin-bottom: 40px;}
        .blog-post { background: #fff; border-radius: 9px; box-shadow: 0 2px 8px #0001; padding: 26px 22px; }
        .blog-post h3 { margin: 0 0 10px 0; color: var(--primary);}
        .blog-post p { margin: 0 0 5px 0; }
        .blog-post a { color: var(--secondary); font-weight: bold; text-decoration: underline;}
        .news-list { margin: 40px auto; max-width: 900px;}
        .news-item { background: #fff; border-radius: 9px; box-shadow: 0 2px 8px #0001; padding: 22px; margin-bottom: 18px;}
        .news-item h3 { margin: 0 0 7px 0; color: var(--primary);}
        .news-item .date { font-size: 0.96rem; color: #888; margin-bottom: 4px;}
        .news-item p { margin: 0 0 6px 0;}
        .news-item a { color: var(--secondary); font-weight: bold; text-decoration: underline;}
        footer { background-color: var(--dark); color: white; padding: 60px 0 30px; }
        .footer-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 40px; }
        .footer-col h3 { font-size: 1.3rem; margin-bottom: 20px; position: relative; padding-bottom: 10px; }
        .footer-col h3::after { content: ''; position: absolute; bottom: 0; left: 0; width: 50px; height: 2px; background-color: var(--secondary); }
        .footer-col ul { list-style: none; }
        .footer-col ul li { margin-bottom: 10px; }
        .footer-col a { color: #ddd; text-decoration: none; transition: color 0.3s; }
        .footer-col a:hover { color: var(--secondary); }
        .social-links { display: flex; gap: 15px; margin-top: 20px; }
        .social-links a:focus { outline: 2px solid var(--secondary);}
        .social-links a { display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 50%; background-color: rgba(255,255,255,0.1); color: white; transition: all 0.3s; }
        .social-links a:hover { background-color: var(--secondary); transform: translateY(-3px); }
        .copyright { text-align: center; padding-top: 30px; border-top: 1px solid rgba(255,255,255,0.1); font-size: 0.9rem; color: #aaa; }
    </style>
    <script src="/assets/js/language.js"></script>
    <script src="/assets/js/language-selector.js"></script>
    <script src="/assets/js/language-transitions.js"></script>
    <script src="/assets/js/navigation.js"></script>
    <link rel="alternate" hreflang="nl" href="/blog.html">
    <link rel="alternate" hreflang="pt" href="/pt/blog.html">
    <link rel="canonical" href="/en/blog.html">
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-top">
            <div class="container">
                <div class="contact-info" aria-label="Contact information">
                    <a href="mailto:<EMAIL>" aria-label="E-mail ICT Tilburg"><i class="fas fa-envelope"></i> <EMAIL></a>
                    <a href="tel:+31612345678" aria-label="Call ICT Tilburg"><i class="fas fa-phone"></i> +31 6 1234 5678</a>
                </div>
                <div class="language-selector" role="navigation" aria-label="Language selection">
                    <button class="lang-btn" aria-pressed="false">NL</button>
                    <button class="lang-btn" aria-pressed="false">PT</button>
                    <button class="lang-btn active" aria-pressed="true">EN</button>
                </div>
            </div>
        </div>
        <div class="main-header">
            <div class="container">
                <div class="logo-area">
                    <div class="logo" aria-label="Logo">
                        <i class="fas fa-hands-helping"></i>
                    </div>
                    <div class="logo-text">ICT<span>Tilburg</span></div>
                </div>
                <nav aria-label="Main menu">
                    <button class="mobile-menu-btn" aria-label="Open menu" aria-expanded="false">
                        <i class="fas fa-bars"></i>
                    </button>
                    <ul>
                        <li><a href="index.html#home" tabindex="0">Home</a></li>
                        <li><a href="services.html" tabindex="0">Services</a></li>
                        <li><a href="about-us.html" tabindex="0">About Us</a></li>
                        <li><a href="workshops.html" tabindex="0">Workshops</a></li>
                        <li><a href="blog.html" tabindex="0">Blog & News</a></li>
                        <li><a href="contact.html" tabindex="0">Contact</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>
    <!-- BLOG SECTION -->
    <main>
        <section class="section" id="blog" aria-label="Blog">
            <div class="container">
                <div class="section-title">
                    <h2>Blog</h2>
                    <p>Read and share stories, tips, and digital experiences from the ICT Tilburg community.</p>
                </div>
                <section class="onboarding-section" aria-label="Want to share your story?">
                    <h3>Want to share your experience?</h3>
                    <p>Fill in your story, tip, or experience below. We read everything with care and love to publish great stories from the community!</p>
                </section>
                <!-- Example list of blog posts (add your own as needed) -->
                <div class="blog-list">
                    <div class="blog-post">
                        <h3>How I Learned to Use Email at 70</h3>
                        <p>By Maria Jansen</p>
                        <p>From zero to email hero! My story of learning digital skills with the help of ICT Tilburg.</p>
                        <a href="#">Read more</a>
                    </div>
                    <div class="blog-post">
                        <h3>Safe Online Banking: My Tips</h3>
                        <p>By Ahmed B.</p>
                        <p>Tips for safe internet banking I learned at a workshop, and how you can do it too.</p>
                        <a href="#">Read more</a>
                    </div>
                    <div class="blog-post">
                        <h3>Why I Switched to Open Source</h3>
                        <p>By Laura V.</p>
                        <p>Free software for everyone! My transition and favorite free alternatives.</p>
                        <a href="#">Read more</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- NEWS SECTION -->
        <section class="section" id="news" aria-label="News & Updates">
            <div class="container">
                <div class="section-title">
                    <h2>News & Updates</h2>
                    <p>The latest news, events, and technology updates from ICT Tilburg and the community.</p>
                </div>
                <section class="onboarding-section" aria-label="How to stay updated">
                    <h3>Stay up to date!</h3>
                    <ul>
                        <li>Check this page for upcoming events and news.</li>
                        <li>Want to share your own news? <a href="contact.html">Contact us</a>!</li>
                        <li>Subscribe to our newsletter for regular updates.</li>
                    </ul>
                </section>
                <div class="news-list">
                    <div class="news-item">
                        <div class="date">June 1, 2025</div>
                        <h3>Summer Workshop Series Announced</h3>
                        <p>We're offering free workshops on digital safety and open source this summer. <a href="workshops.html">View schedule</a></p>
                    </div>
                    <div class="news-item">
                        <div class="date">May 20, 2025</div>
                        <h3>ICT Tilburg at Senior Day 2025</h3>
                        <p>Thank you for visiting our stand! Download workshop materials in our <a href="downloads.html">downloads section</a>.</p>
                    </div>
                    <div class="news-item">
                        <div class="date">May 10, 2025</div>
                        <h3>New: Community Q&A Forum</h3>
                        <p>Ask your digital questions and help others in our new <a href="forum.html#forum">community forum</a>. You can also directly <a href="forum.html#ask-question">post a question</a> or check our <a href="forum.html#faq">forum FAQ</a>.</p>
                    </div>
                </div>
            </div>
        </section>
        <section class="faq-section" id="news-faq" aria-label="News FAQ">
            <div class="container">
                <h2>News FAQ</h2>
                <div class="faq-list">
                    <details>
                        <summary>How often is this page updated?</summary>
                        <p>We update the news section at least once a month and after each event.</p>
                    </details>
                    <details>
                        <summary>How can I submit my own news or event?</summary>
                        <p>Contact us via the <a href="contact.html">contact page</a> with your news or event details.</p>
                    </details>
                    <details>
                        <summary>Can I receive news by email?</summary>
                        <p>Yes! Subscribe to our newsletter at the bottom of this page.</p>
                    </details>
                </div>
            </div>
        </section>
    </main>
    <!-- Footer -->
    <footer role="contentinfo">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <div class="logo-area">
                        <div class="logo" aria-label="Logo">
                            <i class="fas fa-hands-helping" aria-hidden="true"></i>
                        </div>
                        <div class="logo-text">ICT<span>Tilburg</span></div>
                    </div>
                    <p>Free, innovative IT support in Tilburg and surroundings.</p>
                    <div class="social-links" aria-label="Social media">
                        <a href="#" tabindex="0" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" tabindex="0" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" tabindex="0" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" tabindex="0" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-col">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html#home">Home</a></li>
                        <li><a href="services.html">Services</a></li>
                        <li><a href="about-us.html">About Us</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                        <li><a href="blog.html">Blog</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Our Services</h3>
                    <ul>
                        <li><a href="remote-support.html">Remote Support</a></li>
                        <li><a href="open-source.html">Open Source</a></li>
                        <li><a href="business-management.html">Business Management</a></li>
                        <li><a href="ai-automation.html">AI & Automation</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Newsletter</h3>
                    <p>Sign up for news, tips, and invitations to free workshops.</p>
                    <form>
                        <div class="form-group">
                            <input type="email" class="form-control" placeholder="Your email" required>
                        </div>
                        <button type="submit" class="btn">Sign Up</button>
                    </form>
                </div>
            </div>
            <div class="copyright">
                <p>&copy; 2025 ICT Tilburg. All rights reserved. | Technology for everyone, community first.</p>
            </div>
        </div>
    </footer>
    <script>
        // Mobile Menu Toggle with ARIA
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const navMenu = document.querySelector('nav ul');
        if (mobileMenuBtn && navMenu) {
            mobileMenuBtn.addEventListener('click', () => {
                navMenu.classList.toggle('show');
                const expanded = mobileMenuBtn.getAttribute('aria-expanded') === 'true';
                mobileMenuBtn.setAttribute('aria-expanded', (!expanded).toString());
            });
        }
        // Language switcher
        const langButtons = document.querySelectorAll('.lang-btn');
        langButtons.forEach(button => {
            button.addEventListener('click', () => {
                langButtons.forEach(btn => btn.classList.remove('active'));
                langButtons.forEach(btn => btn.setAttribute('aria-pressed', 'false'));
                button.classList.add('active');
                button.setAttribute('aria-pressed', 'true');
            });
        });
    </script>
</body>
</html>