<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>ICT Tilburg - Contact</title>
    <meta name="description" content="Contact ICT Tilburg for free digital help, advice, or to sign up for a workshop. We answer quickly!">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Styles and fonts -->
    <link rel="icon" type="image/png" href="favicon.png">
    <link rel="stylesheet" href="/assets/css/layout-updates.css">
    <link rel="stylesheet" href="/assets/css/language-transitions.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #2c5e92;
            --secondary: #43b380;
            --accent: #ff6b35;
            --light: #f8f9fa;
            --dark: #212529;
            --gray: #6c757d;
            --font-main: 'Open Sans', sans-serif;
            --font-heading: 'Roboto', sans-serif;
        }
        html { font-size: 18px; }
        body { font-family: var(--font-main); color: var(--dark); background-color: #f0f4f8; }
        .container { width: 95%; max-width: 1200px; margin: 0 auto; }
        header, footer { background-color: white; }
        .header-top { background-color: var(--primary); color: white; padding: 8px 0; font-size: 1rem; }
        .header-top .container { display: flex; justify-content: space-between; align-items: center; }
        .contact-info { display: flex; gap: 20px; }
        .contact-info a { color: white; text-decoration: none; }
        .language-selector { display: flex; gap: 10px; }
        .lang-btn { background: none; border: none; color: white; cursor: pointer; font-size: 1rem; opacity: 0.7; }
        .lang-btn.active, .lang-btn:focus { opacity: 1; font-weight: 600; text-decoration: underline; }
        .main-header { padding: 15px 0; }
        .logo-area { display: flex; align-items: center; gap: 15px; }
        .logo { width: 60px; height: 60px; background-color: var(--secondary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; }
        .logo-text { font-family: var(--font-heading); font-weight: 700; font-size: 2rem; color: var(--primary); }
        .logo-text span { color: var(--secondary); }
        nav ul { display: flex; list-style: none; gap: 25px; }
        nav a { text-decoration: none; color: var(--dark); font-weight: 600; font-size: 1.15rem; transition: color 0.3s; position: relative; }
        nav a:focus { outline: 2px solid var(--secondary); outline-offset: 2px; }
        nav a:hover { color: var(--primary); }
        nav a::after { content: ''; position: absolute; bottom: -5px; left: 0; width: 0; height: 2px; background-color: var(--primary); transition: width 0.3s; }
        nav a:hover::after { width: 100%; }
        .mobile-menu-btn { display: none; background: none; border: none; font-size: 1.7rem; cursor: pointer; color: var(--primary); }
        @media (max-width: 900px) {
            .about-content { flex-direction: column; gap: 35px; }
        }
        @media (max-width: 768px) {
            nav ul { display: none; position: absolute; background: #fff; left: 0; width: 100%; flex-direction: column; gap: 0; top: 100%; z-index: 100; }
            nav ul.show { display: flex; }
            .mobile-menu-btn { display: block; }
        }
        .section { padding: 80px 0 40px 0; }
        .section-title { text-align: center; margin-bottom: 50px; }
        .section-title h2 { font-size: 2rem; color: var(--primary); margin-bottom: 15px; position: relative; display: inline-block; }
        .section-title h2::after { content: ''; position: absolute; bottom: -10px; left: 50%; transform: translateX(-50%); width: 80px; height: 4px; background-color: var(--secondary); }
        .section-title p { color: var(--gray); max-width: 700px; margin: 20px auto 0; }
        .onboarding-highlight, .faq-section {
            background: #eafbf3;
            border-left: 5px solid #43b380;
            padding: 18px 28px;
            margin-bottom: 38px;
            border-radius: 8px;
            max-width: 900px;
            margin-left: auto;
            margin-right: auto;
        }
        .onboarding-highlight strong { color: var(--primary);}
        .onboarding-highlight ol { margin: 13px 0 10px 18px; }
        .onboarding-highlight a { color: var(--secondary); font-weight: bold; text-decoration: underline; }
        .faq-section { margin-top: 60px; }
        .faq-section h2 { color: var(--primary);}
        .faq-list details { margin-bottom: 12px; background: #fff; border-radius: 5px; padding: 7px 14px;}
        .faq-list summary { font-weight: 600; cursor: pointer;}
        .faq-list p { margin: 10px 0 5px 0; color: var(--dark);}
        form.contact-form { background: #fff; border-radius: 9px; box-shadow: 0 2px 8px #0001; padding: 32px 24px; margin: 40px auto 0; max-width: 500px; }
        .form-group { margin-bottom: 22px; }
        .form-label { display: block; font-weight: 600; margin-bottom: 7px; color: var(--primary);}
        .form-control, textarea.form-control { width: 100%; padding: 10px 13px; font-size: 1rem; border: 1px solid #bcd6c7; border-radius: 5px; }
        textarea.form-control { min-height: 100px; }
        .btn { background: var(--secondary); color: #fff; font-weight: bold; border: none; border-radius: 18px; padding: 10px 32px; cursor: pointer; }
        .btn:hover { background: var(--primary);}
        footer { background-color: var(--dark); color: white; padding: 60px 0 30px; }
        .footer-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 40px; }
        .footer-col h3 { font-size: 1.3rem; margin-bottom: 20px; position: relative; padding-bottom: 10px; }
        .footer-col h3::after { content: ''; position: absolute; bottom: 0; left: 0; width: 50px; height: 2px; background-color: var(--secondary); }
        .footer-col ul { list-style: none; }
        .footer-col ul li { margin-bottom: 10px; }
        .footer-col a { color: #ddd; text-decoration: none; transition: color 0.3s; }
        .footer-col a:hover { color: var(--secondary); }
        .social-links { display: flex; gap: 15px; margin-top: 20px; }
        .social-links a:focus { outline: 2px solid var(--secondary);}
        .social-links a { display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 50%; background-color: rgba(255,255,255,0.1); color: white; transition: all 0.3s; }
        .social-links a:hover { background-color: var(--secondary); transform: translateY(-3px); }
        .copyright { text-align: center; padding-top: 30px; border-top: 1px solid rgba(255,255,255,0.1); font-size: 0.9rem; color: #aaa; }
    </style>
    <script src="/assets/js/language.js"></script>
    <script src="/assets/js/language-selector.js"></script>
    <script src="/assets/js/language-transitions.js"></script>
    <script src="/assets/js/navigation.js"></script>
    <link rel="alternate" hreflang="nl" href="/contact.html">
    <link rel="alternate" hreflang="pt" href="/pt/contact.html">
    <link rel="canonical" href="/en/contact.html">
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-top">
            <div class="container">
                <div class="contact-info" aria-label="Contact information">
                    <a href="mailto:<EMAIL>" aria-label="E-mail ICT Tilburg"><i class="fas fa-envelope"></i> <EMAIL></a>
                    <a href="tel:+31612345678" aria-label="Call ICT Tilburg"><i class="fas fa-phone"></i> +31 6 1234 5678</a>
                </div>
                <div class="language-selector" role="navigation" aria-label="Language selection">
                    <button class="lang-btn" aria-pressed="false">NL</button>
                    <button class="lang-btn" aria-pressed="false">PT</button>
                    <button class="lang-btn active" aria-pressed="true">EN</button>
                </div>
            </div>
        </div>
        <div class="main-header">
            <div class="container">
                <div class="logo-area">
                    <div class="logo" aria-label="Logo">
                        <i class="fas fa-hands-helping"></i>
                    </div>
                    <div class="logo-text">ICT<span>Tilburg</span></div>
                </div>
                <nav aria-label="Main menu">
                    <button class="mobile-menu-btn" aria-label="Open menu" aria-expanded="false">
                        <i class="fas fa-bars"></i>
                    </button>
                    <ul>
                        <li><a href="index.html#home" tabindex="0">Home</a></li>
                        <li><a href="services.html" tabindex="0">Services</a></li>
                        <li><a href="about-us.html" tabindex="0">About Us</a></li>
                        <li><a href="workshops.html" tabindex="0">Workshops</a></li>
                        <li><a href="blog.html" tabindex="0">Blog & News</a></li>
                        <li><a href="contact.html" tabindex="0">Contact</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>
    <!-- CONTACT SECTION -->
    <main>
        <section class="section" id="contact" aria-label="Contact">
            <div class="container">
                <div class="section-title">
                    <h2>Contact</h2>
                    <p>Get in touch for free digital help, advice or to register for a workshop. We will respond as soon as possible!</p>
                </div>
                <div class="onboarding-highlight" aria-label="First time contact?">
                    <strong>First time? Here’s how it works:</strong>
                    <ol>
                        <li>Fill in your name and email</li>
                        <li>Describe your question or problem</li>
                        <li>We will respond via email, phone, or WhatsApp</li>
                    </ol>
                    <p>You can also call or message us directly via WhatsApp!</p>
                </div>
                <form class="contact-form" aria-label="Contact form" method="POST" action="#">
                    <div class="form-group">
                        <label class="form-label" for="name">Your Name</label>
                        <input class="form-control" type="text" id="name" name="name" required placeholder="Your full name">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="email">Your Email</label>
                        <input class="form-control" type="email" id="email" name="email" required placeholder="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="message">Message</label>
                        <textarea class="form-control" id="message" name="message" required placeholder="Describe your question, problem, or request"></textarea>
                    </div>
                    <button type="submit" class="btn">Send</button>
                </form>
            </div>
        </section>
        <section class="faq-section" id="faq" aria-label="Frequently Asked Questions about contact">
            <h2>Contact FAQ</h2>
            <div class="faq-list">
                <details>
                    <summary>How quickly do I get a response?</summary>
                    <p>We usually respond within 24 hours, often faster.</p>
                </details>
                <details>
                    <summary>Can I also contact you via WhatsApp or phone?</summary>
                    <p>Yes, of course! See our contact details for all available channels.</p>
                </details>
                <details>
                    <summary>Do I need to describe my problem right away?</summary>
                    <p>Please do! The more information you provide, the faster we can help.</p>
                </details>
                <details>
                    <summary>Can I ask a question anonymously?</summary>
                    <p>Yes, but for personal help it’s helpful to include your name and email.</p>
                </details>
            </div>
        </section>
    </main>
    <!-- Footer -->
    <footer role="contentinfo">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <div class="logo-area">
                        <div class="logo" aria-label="Logo">
                            <i class="fas fa-hands-helping" aria-hidden="true"></i>
                        </div>
                        <div class="logo-text">ICT<span>Tilburg</span></div>
                    </div>
                    <p>Free, innovative IT support in Tilburg and surroundings.</p>
                    <div class="social-links" aria-label="Social media">
                        <a href="#" tabindex="0" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" tabindex="0" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" tabindex="0" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" tabindex="0" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-col">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html#home">Home</a></li>
                        <li><a href="services.html">Services</a></li>
                        <li><a href="about-us.html">About Us</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                        <li><a href="blog.html">Blog</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Our Services</h3>
                    <ul>
                        <li><a href="remote-support.html">Remote Support</a></li>
                        <li><a href="open-source.html">Open Source</a></li>
                        <li><a href="business-management.html">Business Management</a></li>
                        <li><a href="ai-automation.html">AI & Automation</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Newsletter</h3>
                    <p>Sign up for news, tips, and invitations to free workshops.</p>
                    <form>
                        <div class="form-group">
                            <input type="email" class="form-control" placeholder="Your email" required>
                        </div>
                        <button type="submit" class="btn">Sign Up</button>
                    </form>
                </div>
            </div>
            <div class="copyright">
                <p>&copy; 2025 ICT Tilburg. All rights reserved. | Technology for everyone, community first.</p>
            </div>
        </div>
    </footer>
    <script>
        // Mobile Menu Toggle with ARIA
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const navMenu = document.querySelector('nav ul');
        if (mobileMenuBtn && navMenu) {
            mobileMenuBtn.addEventListener('click', () => {
                navMenu.classList.toggle('show');
                const expanded = mobileMenuBtn.getAttribute('aria-expanded') === 'true';
                mobileMenuBtn.setAttribute('aria-expanded', (!expanded).toString());
            });
        }
        // Language switcher
        const langButtons = document.querySelectorAll('.lang-btn');
        langButtons.forEach(button => {
            button.addEventListener('click', () => {
                langButtons.forEach(btn => btn.classList.remove('active'));
                langButtons.forEach(btn => btn.setAttribute('aria-pressed', 'false'));
                button.classList.add('active');
                button.setAttribute('aria-pressed', 'true');
            });
        });
    </script>
</body>
</html>