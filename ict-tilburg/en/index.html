<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICT Tilburg - Free IT Support & Innovation</title>
    <meta name="description" content="ICT Tilburg offers free and innovative IT support for everyone in Tilburg and surroundings. Accessibility, inclusion and digital skills for all.">
    <meta property="og:title" content="ICT Tilburg - Free IT Support & Innovation">
    <meta property="og:description" content="ICT Tilburg offers free and innovative IT support for everyone in Tilburg and surroundings.">
    <meta property="og:image" content="https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=600&q=80">
    <link rel="icon" type="image/png" href="favicon.png">
    <link rel="stylesheet" href="/assets/css/layout-updates.css">
    <link rel="stylesheet" href="/assets/css/language-transitions.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #174080;
            --secondary: #43b380;
            --accent: #ff6b35;
            --light: #f8f9fa;
            --dark: #212529;
            --gray: #6c757d;
            --danger: #e74c3c;
            --success: #43b380;
            --font-main: 'Open Sans', Arial, sans-serif;
            --font-heading: 'Roboto', Arial, sans-serif;
        }
        html { font-size: 18px; }
        body { font-family: var(--font-main); color: var(--dark); background-color: #f0f4f8; }
        .container { width: 95%; max-width: 1200px; margin: 0 auto; }
        header { background: #fff }
        .header-top { background-color: var(--primary); color: white; padding: 8px 0; font-size: 1rem; }
        .header-top .container { display: flex; justify-content: space-between; align-items: center; }
        .contact-info { display: flex; gap: 20px; }
        .contact-info a { color: white; text-decoration: none; }
        .language-selector { display: flex; gap: 10px; }
        .lang-btn { background: none; border: none; color: white; cursor: pointer; font-size: 1rem; opacity: 0.7; }
        .lang-btn.active, .lang-btn:focus { opacity: 1; font-weight: 600; text-decoration: underline; }
        .main-header { padding: 15px 0; }
        .logo-area { display: flex; align-items: center; gap: 15px; }
        .logo { width: 60px; height: 60px; background-color: var(--secondary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; }
        .logo-text { font-family: var(--font-heading); font-weight: 700; font-size: 2rem; color: var(--primary); }
        .logo-text span { color: var(--secondary); }
        nav ul { display: flex; list-style: none; gap: 25px; }
        nav a { text-decoration: none; color: var(--dark); font-weight: 600; font-size: 1.15rem; transition: color 0.3s; position: relative; }
        nav a:focus { outline: 2px solid var(--secondary); outline-offset: 2px; }
        nav a:hover { color: var(--primary); }
        nav a::after { content: ''; position: absolute; bottom: -5px; left: 0; width: 0; height: 2px; background-color: var(--primary); transition: width 0.3s; }
        nav a:hover::after { width: 100%; }
        .mobile-menu-btn { display: none; background: none; border: none; font-size: 1.7rem; cursor: pointer; color: var(--primary); }
        @media (max-width: 768px) {
            nav ul { display: none; position: absolute; background: #fff; left: 0; width: 100%; flex-direction: column; gap: 0; top: 100%; z-index: 100; }
            nav ul.show { display: flex; }
            .mobile-menu-btn { display: block; }
        }
        .hero { background: linear-gradient(rgba(23,64,128, 0.85),rgba(23,64,128,0.88)), url('https://images.unsplash.com/photo-1550751827-4bd374c3f58b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1500&q=80') center/cover no-repeat; color: white; padding: 70px 0 50px 0; text-align: center; }
        .hero h1 { font-size: 2.7rem; margin-bottom: 18px; font-weight: 700; }
        .hero p { font-size: 1.2rem; margin-bottom: 22px; }
        .btn { display: inline-block; background-color: var(--accent); color: white; padding: 12px 30px; border-radius: 50px; text-decoration: none; font-weight: 700; font-size: 1.15rem; border: 2px solid var(--accent); transition: all 0.3s; margin: 0 8px 8px 0; }
        .btn:focus { outline: 2px solid var(--secondary); }
        .btn:hover, .btn:active { background-color: var(--success); color: #fff; border-color: var(--success);}
        .btn-secondary { background-color: transparent; color: var(--accent); border: 2px solid var(--accent);}
        .btn-secondary:hover { background-color: var(--accent); color: #fff; }
        .section { padding: 70px 0; }
        .section-title { text-align: center; margin-bottom: 48px; }
        .section-title h2 { font-size: 2rem; color: var(--primary); margin-bottom: 15px; position: relative; display: inline-block; }
        .section-title h2::after { content: ''; position: absolute; bottom: -10px; left: 50%; transform: translateX(-50%); width: 80px; height: 4px; background-color: var(--secondary); }
        .section-title p { color: var(--gray); max-width: 700px; margin: 20px auto 0; }
        .services-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; }
        .service-card { background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0,0,0,0.07); transition: transform 0.3s; }
        .service-card:hover { transform: translateY(-10px); }
        .service-icon { background-color: var(--primary); color: white; font-size: 2.2rem; height: 110px; display: flex; align-items: center; justify-content: center; }
        .service-content { padding: 25px; }
        .service-content h3 { font-size: 1.25rem; margin-bottom: 12px; color: var(--primary); }
        .contact-form { background-color: #f8f9fa; padding: 30px; border-radius: 10px; margin-top: 30px;}
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 600; }
        .form-control { width: 100%; padding: 12px 15px; border: 1.5px solid #ddd; border-radius: 5px; font-family: var(--font-main); font-size: 1.08rem; }
        .form-control:focus { border-color: var(--primary); outline: 2px solid var(--primary); }
        .form-control.invalid { border-color: var(--danger);}
        .form-message { display: none; margin-bottom: 18px; padding: 10px; border-radius: 5px; font-weight: 600;}
        .form-message.success { display: block; background: #d9f8eb; color: var(--success);}
        .form-message.error { display: block; background: #ffe5e5; color: var(--danger);}
        .about-content { display: grid; grid-template-columns: 2fr 1fr; gap: 50px; align-items: start; }
        .about-text h2 { font-size: 2rem; color: var(--primary); margin-bottom: 20px; }
        .highlight { background-color: #e8f4f8; padding: 20px; border-left: 4px solid var(--secondary); margin: 25px 0; }
        .steps { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px; margin-top: 30px; }
        .step { text-align: center; }
        .step-number { background-color: var(--secondary); color: white; width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.5rem; font-weight: 700; margin: 0 auto 15px; }
        .step h4 { margin-bottom: 10px; color: var(--primary); }
        .about-image img { width: 100%; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .testimonial-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; }
        .testimonial { background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.07); }
        .testimonial p { font-style: italic; margin-bottom: 20px; }
        .client { display: flex; align-items: center; gap: 15px; }
        .client-avatar { width: 50px; height: 50px; background-color: var(--secondary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; }
        .client-info h4 { margin-bottom: 5px; color: var(--primary); }
        .client-info p { font-size: 0.9rem; color: var(--gray); }
        .contact-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 50px; }
        .contact-item { display: flex; align-items: start; gap: 20px; margin-bottom: 30px; }
        .contact-icon { background-color: var(--secondary); color: white; width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.2rem; }
        .contact-item h3 { margin-bottom: 8px; color: var(--primary); }
        footer { background-color: var(--dark); color: white; padding: 60px 0 30px; }
        .footer-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 40px; }
        .footer-col h3 { font-size: 1.3rem; margin-bottom: 20px; position: relative; padding-bottom: 10px; }
        .footer-col h3::after { content: ''; position: absolute; bottom: 0; left: 0; width: 50px; height: 2px; background-color: var(--secondary); }
        .footer-col ul { list-style: none; }
        .footer-col ul li { margin-bottom: 10px; }
        .footer-col a { color: #ddd; text-decoration: none; transition: color 0.3s; }
        .footer-col a:hover { color: var(--secondary); }
        .social-links { display: flex; gap: 15px; margin-top: 20px; }
        .social-links a { display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 50%; background-color: rgba(255,255,255,0.1); color: white; transition: all 0.3s; }
        .social-links a:focus { outline: 2px solid var(--secondary); }
        .social-links a:hover { background-color: var(--secondary); transform: translateY(-3px); }
        .copyright { text-align: center; padding-top: 30px; border-top: 1px solid rgba(255,255,255,0.1); font-size: 0.9rem; color: #aaa; }
        @media (max-width: 768px) {
            .about-content, .contact-grid { grid-template-columns: 1fr; }
            .hero h1 { font-size: 2.2rem; }
            .steps { grid-template-columns: 1fr; }
        }
    </style>
    <script src="/assets/js/language.js"></script>
    <script src="/assets/js/language-selector.js"></script>
    <script src="/assets/js/language-transitions.js"></script>
    <script src="/assets/js/navigation.js"></script>
    <link rel="alternate" hreflang="nl" href="/index.html">
    <link rel="alternate" hreflang="pt" href="/pt/index.html">
    <link rel="canonical" href="/en/index.html">
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-top">
            <div class="container">
                <div class="contact-info">
                    <a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> <EMAIL></a>
                    <a href="tel:+31612345678"><i class="fas fa-phone"></i> +31 6 1234 5678</a>
                </div>
                <div class="language-selector">
                    <a href="#" data-lang="en" class="active">EN</a>
                    <a href="../index.html" data-lang="nl">NL</a>
                    <a href="../pt/index.html" data-lang="pt">PT</a>
                </div>
            </div>
        </div>
        <div class="main-header">
            <div class="container">
                <div class="logo-area">
                    <div class="logo">
                        <i class="fas fa-hands-helping"></i>
                    </div>
                    <div class="logo-text">ICT<span>Tilburg</span></div>
                </div>
                <nav>
                    <div class="logo">
                        <a href="index.html">ICT Tilburg</a>
                    </div>
                    <ul class="nav-links">
                        <li><a href="index.html">Home</a></li>
                        <li><a href="about-us.html">About Us</a></li>
                        <li><a href="services.html">Services</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                        <li><a href="blog.html">Blog & News</a></li>
                        <li><a href="forum.html">Forum</a></li>
                        <li><a href="downloads.html">Downloads</a></li>
                        <li><a href="partners.html">Partners</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <h1>Technology that Connects, Solutions that Transform</h1>
            <p>Free and specialized IT support for the community of Tilburg and surroundings.<br>
            For seniors, people with low income, non-profits, and everyone seeking reliable technical help through open source specialists and innovation for all.</p>
            <div class="hero-buttons">
                <a href="contact.html" class="btn">Need Help Now</a>
                <a href="services.html" class="btn btn-secondary">View Our Services</a>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="section" id="services">
        <div class="container">
            <div class="section-title">
                <h2>Our Services</h2>
                <p>We solve every IT problem with empathy, expertise and creativity. Technical support, development, consulting and training — always customized.</p>
            </div>
            <div class="services-grid">
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="service-content">
                        <h3>Multichannel Remote Support</h3>
                        <p>Email, chat, WhatsApp or phone — always available, wherever you are.</p>
                    </div>
                </div>
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fab fa-linux"></i>
                    </div>
                    <div class="service-content">
                        <h3>Open Source Experts</h3>
                        <p>Installation, configuration and training in Linux, BSD and free software for users and businesses.</p>
                    </div>
                </div>
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <div class="service-content">
                        <h3>Commercial & Industrial Management</h3>
                        <p>Administrative, commercial and industrial management solutions adapted to your situation.</p>
                    </div>
                </div>
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="service-content">
                        <h3>Artificial Intelligence & Automation</h3>
                        <p>Development of custom AI applications for more efficient businesses.</p>
                    </div>
                </div>
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="service-content">
                        <h3>Modern Websites</h3>
                        <p>Development of accessible websites and web shops with results.</p>
                    </div>
                </div>
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-chalkboard-teacher"></i>
                    </div>
                    <div class="service-content">
                        <h3>Workshops & Training</h3>
                        <p>Courses on technology, digital security, open source and more for every level.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="section about" id="about">
        <div class="container">
            <div class="about-content">
                <div class="about-text">
                    <h2>Digital Inclusion, Innovation and Community</h2>
                    <p>ICT Tilburg was founded to break down digital barriers and make technology accessible. With us, seniors, small businesses and non-profits find professional solutions, without costs and with heart for people.</p>
                    <div class="highlight">
                        <p><strong>Mission:</strong> Ensuring that no one is excluded in the digital age. We promote independence, security and innovation through free software and passionate specialists.</p>
                    </div>
                    <p>Besides solving technical problems, we help our users use technology confidently and discover the digital world.</p>
                    <h3>How Does It Work?</h3>
                    <div class="steps">
                        <div class="step">
                            <div class="step-number">1</div>
                            <h4>Get in touch</h4>
                            <p>Tell us your need through your preferred channel.</p>
                        </div>
                        <div class="step">
                            <div class="step-number">2</div>
                            <h4>Personal Analysis</h4>
                            <p>Receive advice and diagnosis from people who understand your world.</p>
                        </div>
                        <div class="step">
                            <div class="step-number">3</div>
                            <h4>Solution & Training</h4>
                            <p>We solve it and teach you to handle technology independently.</p>
                        </div>
                    </div>
                </div>
                <div class="about-image">
                    <img src="https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80" alt="ICT Tilburg team">
                </div>
            </div>
        </div>
    </section>

    <!-- Workshops Section -->
    <section class="section" id="workshops">
        <div class="container">
            <div class="section-title">
                <h2>Workshops & Events</h2>
                <p>Join our free events: online security, Linux, automation, AI in daily life and more. Learn, share experiences and grow together!</p>
            </div>
            <div class="services-grid">
                <div class="service-card">
                    <div class="service-icon"><i class="fas fa-shield-alt"></i></div>
                    <div class="service-content">
                        <h3>Digital Security</h3>
                        <p>Protect yourself against scams and threats on the internet. Practical and for all ages.</p>
                    </div>
                </div>
                <div class="service-card">
                    <div class="service-icon"><i class="fab fa-linux"></i></div>
                    <div class="service-content">
                        <h3>Linux for Beginners</h3>
                        <p>Discover the world of open source and save on licenses.</p>
                    </div>
                </div>
                <div class="service-card">
                    <div class="service-icon"><i class="fas fa-network-wired"></i></div>
                    <div class="service-content">
                        <h3>Automation & Productivity</h3>
                        <p>Practical tools to speed up personal and business tasks.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="section testimonials">
        <div class="container">
            <div class="section-title">
                <h2>Community Experiences</h2>
                <p>Real stories of digital transformation in Tilburg.</p>
            </div>
            <div class="testimonial-grid">
                <div class="testimonial">
                    <p>"I never thought I would learn to work with computers after my 70s. ICT Tilburg helped me with great patience and now I shop online safely."</p>
                    <div class="client">
                        <div class="client-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="client-info">
                            <h4>Maria Lopes</h4>
                            <p>Retired, Tilburg</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial">
                    <p>"We were able to digitize our small business without spending a lot of money. Now we have control over sales and inventory with open source software."</p>
                    <div class="client">
                        <div class="client-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="client-info">
                            <h4>Sebastiaan Jansen</h4>
                            <p>Entrepreneur, Goirle</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial">
                    <p>"The technology workshops not only taught me, but also gave me new friends and confidence to be digital."</p>
                    <div class="client">
                        <div class="client-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="client-info">
                            <h4>Sandra van Dijk</h4>
                            <p>Volunteer, Berkel-Enschot</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="section contact" id="contact">
        <div class="container">
            <div class="section-title">
                <h2>Get in Touch</h2>
                <p>A question, challenge or idea? We're happy to help you find the perfect technological solution! Or ask your question directly in our <a href="forum.html#ask-question">community forum</a>.</p>
            </div>
            <div class="contact-grid">
                <div class="contact-info">
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div>
                            <h3>Location</h3>
                            <p>Community Center Tilburg<br>Heuvelring 122, 5038 CL Tilburg</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div>
                            <h3>Phone</h3>
                            <p>+31 6 1234 5678<br>(Monday to Friday, 9am-5pm)</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div>
                            <h3>Email</h3>
                            <p><EMAIL><br><EMAIL></p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div>
                            <h3>Opening Hours</h3>
                            <p>Monday to Friday: 9:00 - 17:00<br>Saturday: 10:00 - 14:00</p>
                        </div>
                    </div>
                </div>
                <div class="contact-form">
                    <form>
                        <div class="form-group">
                            <label for="name">Name</label>
                            <input type="text" id="name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" id="email" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="phone">Phone</label>
                            <input type="tel" id="phone" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="subject">Subject</label>
                            <select id="subject" class="form-control" required>
                                <option value="">Choose a reason for contact</option>
                                <option value="support">Technical Support</option>
                                <option value="workshop">Workshops & Training</option>
                                <option value="business">Business Solutions</option>
                                <option value="website">Website Development</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="message">Message</label>
                            <textarea id="message" class="form-control" required></textarea>
                        </div>
                        <button type="submit" class="btn">Send Message</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <div class="logo-area">
                        <div class="logo">
                            <i class="fas fa-hands-helping"></i>
                        </div>
                        <div class="logo-text">ICT<span>Tilburg</span></div>
                    </div>
                    <p>Free, innovative IT support in Tilburg and surroundings.</p>
                    <div class="social-links">
                        <a href="https://facebook.com" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="https://twitter.com" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="https://linkedin.com" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                        <a href="https://instagram.com" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-col">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="about-us.html">About Us</a></li>
                        <li><a href="services.html">Services</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                        <li><a href="blog.html">Blog & News</a></li>
                        <li><a href="forum.html">Forum</a></li>
                        <li><a href="downloads.html">