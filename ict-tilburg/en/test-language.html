<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Language Switch Test (English)</title>
    <script src="/assets/js/language.js"></script>
    <script src="/assets/js/language-transitions.js"></script>
    <!-- Include console logging to see what's happening -->
    <script>
        // Add debugging code to verify language switching
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Current language detected:', languageHandler.getCurrentLanguage());
            console.log('Preferred language in localStorage:', languageHandler.getPreferredLanguage());
            
            // Add event listeners to log language switching
            document.querySelectorAll('.language-selector a').forEach(link => {
                link.addEventListener('click', (e) => {
                    console.log('Language link clicked:', link.getAttribute('data-lang'));
                });
            });
        });
    </script>
    <script src="/assets/js/navigation.js"></script>
    <link rel="alternate" hreflang="nl" href="/test-language.html">
    <link rel="alternate" hreflang="pt" href="/pt/test-language.html">
    <link rel="canonical" href="/en/test-language.html">
    <link rel="stylesheet" href="/assets/css/language-transitions.css">
</head>
<body>
    <h1>Language Switch Test Page (English)</h1>
    
    <div class="language-selector">
        <a href="/en/test-language.html" data-lang="en" class="active">EN</a>
        <a href="/test-language.html" data-lang="nl">NL</a>
        <a href="/pt/test-language.html" data-lang="pt">PT</a>
    </div>
    
    <p>This page tests the language switching functionality.</p>
    <p>Open the browser console to see debug information.</p>
    
    <h2>Test Links</h2>
    <ul>
        <li><a href="javascript:void(0)" onclick="languageHandler.redirectToLanguageVersion('en', window.location.pathname)">Switch to EN</a></li>
        <li><a href="javascript:void(0)" onclick="languageHandler.redirectToLanguageVersion('nl', window.location.pathname)">Switch to NL</a></li>
        <li><a href="javascript:void(0)" onclick="languageHandler.redirectToLanguageVersion('pt', window.location.pathname)">Switch to PT</a></li>
    </ul>
</body>
</html>
