# ICT Tilburg Website

This repository contains the website for ICT Tilburg, a non-profit organization providing free IT support and education.

## Language System

The website supports three languages:
- Dutch (NL) - Root directory
- English (EN) - /en/ directory
- Portuguese (PT) - /pt/ directory

### Multi-Language Implementation

The language system has been improved with the following features:

1. **Language Detection**: Detects the user's browser language and redirects to the appropriate version if no preference is stored.
2. **Language Switching**: Users can switch between languages using the language selector in the header.
3. **Language Preference**: User's language preference is stored in localStorage.
4. **Page Mapping**: Pages with different names in different languages are mapped correctly.
5. **Smooth Transitions**: Smooth transition effects when switching languages.

### Language Scripts & Files

- **assets/js/language.js**: Main language handling script
- **assets/templates/standard-nav.html**: Navigation template for Dutch (NL)
- **assets/templates/en-nav.html**: Navigation template for English (EN)
- **assets/templates/pt-nav.html**: Navigation template for Portuguese (PT)
- **assets/css/language-transitions.css**: CSS for smooth language transitions

### Utility Scripts

- **fix-all-languages.sh**: Ensures all HTML files have proper language selectors
- **add-language-transitions.sh**: Adds CSS for smooth transitions to all HTML files
- **validate-language-fixes.sh**: Checks for potential issues in the language implementation
- **start-test-server.sh**: Starts a local server for testing the website

### Testing

For testing the language system, visit these special test pages:
- Dutch: /language-verify.html
- English: /en/language-verify.html
- Portuguese: /pt/language-verify.html

## Website Structure

- Root directory: Dutch (NL) version
- /en/: English version
- /pt/: Portuguese version
- /assets/: CSS, JavaScript, images, and templates

## Getting Started

1. Clone the repository
2. Run `./start-test-server.sh` to start a local development server
3. Open your browser and navigate to http://localhost:8000

## Notes

- All three languages are always shown in the selector
- Smooth transitions are implemented when switching languages
- No pop-ups appear during language switching
- Navigation is consistent across all language versions
