<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <!-- <PERSON>ript to handle the base path for production server -->
    <script src="assets/js/base-path.js"></script>
    <!-- Force immediate redirect to NL -->
    <meta http-equiv="refresh" content="0;url=nl/index.html">
    <title>Redirect to Dutch Version - ICT Tilburg</title>
    <meta name="robots" content="noindex">
    <link rel="canonical" href="nl/index.html">
    <!-- Language alternatives -->
    <link rel="alternate" hreflang="en" href="en/index.html">
    <link rel="alternate" hreflang="pt" href="pt/index.html">
    <link rel="alternate" hreflang="nl" href="nl/index.html">
    <script>
        // Function to get browser language
        function getBrowserLanguage() {
            return (navigator.language || navigator.userLanguage || '').split('-')[0];
        }
        
        // Get base path for the site (handles subpath like /ict-tilburg/)
        function getBasePath() {
            // Check if we're running on the production server
            if (window.location.hostname === '**************') {
                return '/ict-tilburg/';
            }
            return '';
        }
        
        // Function to redirect based on browser language - Always prioritize Dutch (NL)
        function redirectToLanguageVersion() {
            const basePath = getBasePath();
            
            // Immediately redirect to Dutch version by default
            window.location.href = basePath + 'nl/index.html';
            
            // The code below will only execute if the redirect didn't happen immediately
            // Check if user has a stored preference that is explicitly set to English
            const storedLang = localStorage.getItem('preferred_language');
            
            // Only honor English preference, otherwise always use Dutch
            if (storedLang === 'en') {
                window.location.href = basePath + 'en/index.html';
            } else {
                // Default to Dutch (NL) in all other cases
                window.location.href = basePath + 'nl/index.html';
            }
        }
        
        // Set Dutch as the default language in localStorage
        localStorage.setItem('preferred_language', 'nl');
        
        // Update links with base path if needed
        document.addEventListener('DOMContentLoaded', function() {
            const basePath = getBasePath();
            if (basePath) {
                document.getElementById('nl-link').setAttribute('href', basePath + 'nl/index.html');
                document.getElementById('en-link').setAttribute('href', basePath + 'en/index.html');
                document.getElementById('pt-link').setAttribute('href', basePath + 'pt/index.html');
            }
        });
        
        // Execute the redirect function
        redirectToLanguageVersion();
    </script>
</head>
<body>
    <p>Redirecting to the appropriate language version. If you are not redirected automatically, please follow one of these links:</p>
    <ul id="language-links">
        <li><a href="nl/index.html" id="nl-link">Dutch version</a></li>
        <li><a href="en/index.html" id="en-link">English version</a></li>
        <li><a href="pt/index.html" id="pt-link">Portuguese version</a></li>
    </ul>
</body>
</html>
