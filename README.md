# ICT Tilburg Website

This is the official website for ICT Tilburg, a multilingual platform available in Dutch, English, and Portuguese.

## Project Structure

```
/
├── assets/              # Shared resources
│   ├── css/            # Stylesheets
│   ├── js/             # JavaScript files
│   └── images/         # Image assets
├── backend/            # Server-side code
│   └── server.js       # Main server file
├── en/                 # English content
├── nl/                 # Dutch content
├── pt/                 # Portuguese content
└── config.json         # Site configuration
```

## Content Pages

Each language section contains the following pages:
- Homepage (index.html)
- About
- Contact
- Services
- Workshops
- Blog
- Forum
- Downloads
- News
- Partners

## Setup and Development

1. Clone the repository
2. Install dependencies
3. Start the backend server: `node backend/server.js`
4. Open index.html in your browser

## Maintenance

- Each language section maintains parallel content
- Shared resources are in the assets directory
- Configuration settings are in config.json

## Contributing

1. Create a feature branch
2. Make your changes
3. Submit a pull request

## Legal Documents

Each language version includes:
- Privacy Policy
- Terms of Service
- Terms of Use
