# ICT Tilburg Website Structure Update

This document explains the changes made to the ICT Tilburg website structure to implement a consistent multi-language system.

## Changes Implemented

### 1. Created a Dedicated Dutch (NL) Directory

- Previously, Dutch content was in the root directory while other languages had dedicated folders (`/en/`, `/pt/`)
- Now, Dutch content is located in the `/nl/` directory, consistent with other languages
- Original Dutch files at the root now redirect to their counterparts in the `/nl/` directory

### 2. Updated Navigation Structure

- Created a new Dutch navigation template (`assets/templates/nl-nav.html`)
- Updated all internal links in Dutch pages to use the new `/nl/` directory paths
- Updated language selector buttons to point to the correct directories

### 3. Language Detection and Switching

- Enhanced the language detection system to recognize the `/nl/` directory
- Modified language switching logic to handle the new directory structure
- Updated the root `index.html` to intelligently redirect users based on:
  - Previous language preference stored in localStorage
  - Browser language settings
  - Default to Dutch if no preference is detected

### 4. Configuration Updates

- Updated `config.json` to include `defaultPath: "/nl/"` for the Dutch language

## Directory Structure Now

```
/ (root)
├── index.html (smart redirect to language-specific version)
├── [other redirect files]
│
├── /nl/ (Dutch content)
│   ├── index.html
│   ├── over-ons.html
│   ├── diensten.html
│   └── ...
│
├── /en/ (English content)
│   ├── index.html
│   ├── about-us.html
│   ├── services.html
│   └── ...
│
├── /pt/ (Portuguese content)
│   ├── index.html
│   ├── sobre-nos.html
│   ├── servicos.html
│   └── ...
│
└── /assets/
    ├── /css/
    ├── /js/
    │   ├── language.js (updated)
    │   └── navigation.js
    ├── /images/
    └── /templates/
        ├── nl-nav.html (new)
        ├── en-nav.html
        └── pt-nav.html
```

## Maintenance Scripts

Several scripts were created or updated to help maintain the website structure:

- `update-nl-directory-navigation.sh` - Updates navigation in Dutch pages in the `/nl/` directory
- `update-nl-internal-links.sh` - Fixes internal links in Dutch pages to use the `/nl/` directory
- `update-language-selectors.sh` - Updates language selectors in EN and PT pages to point to the `/nl/` directory
- `update-all-languages.sh` - Updates navigation for all language versions

## Benefits of the New Structure

1. **Consistency**: All language content follows the same directory structure
2. **Maintainability**: Clear separation between languages makes it easier to update content
3. **Scalability**: Adding new languages in the future will be more straightforward
4. **SEO Optimization**: Clearer URL structure for search engines to understand language versions

## Default Language Configuration

The website has been configured to use Dutch (NL) as the default language in multiple ways:

1. **Root Index Redirect**: The root `index.html` has an immediate meta refresh to the NL version
2. **LocalStorage Setting**: The default language is set to 'nl' in localStorage
3. **Language Detection**: The language detection system prioritizes Dutch for any non-specific paths
4. **Meta Tags**: Canonical links point to the NL versions of pages
5. **Verification**: Created `verify-nl-default.sh` script to verify Dutch is set as default

These measures ensure all visitors will be directed to the Dutch version by default unless they have explicitly selected another language.

## Future Recommendations

1. **Automated Testing**: Implement automated tests to verify all language links work correctly
2. **Content Synchronization**: Consider implementing a system to flag when content in one language is updated but not in others
3. **Language Cookies**: Consider using cookies in addition to localStorage for language preferences to handle server-side detection

## Troubleshooting

### Resolving "URL NOT FOUND" Errors

If you encounter "URL NOT FOUND" errors when accessing the website:

1. **Use Relative Paths**: All URLs in the website should use relative paths (without leading slashes) for local development
   - Correct: `href="nl/index.html"` 
   - Incorrect: `href="/nl/index.html"`

2. **Start the Local Server Correctly**: Always run the server from the project root directory
   - Use the provided script: `./start-local-server.sh`
   - Access the site at `http://localhost:8080`

3. **Check File Existence**: Ensure all referenced files exist in the correct directories
   - Run `test-url-paths.sh` to verify file paths and URL accessibility

4. **Browser Cache**: Clear your browser cache if you continue to see errors after fixes

5. **Fix URL Paths**: If issues persist, run the `fix-url-paths.sh` script to convert absolute paths to relative paths

### Other Common Issues

- If language switching doesn't work correctly, check the `language.js` file and language selectors
- If internal links are broken, run the `update-nl-internal-links.sh` script
- If navigation is inconsistent, run the appropriate update-navigation scripts
- If the default language is not Dutch, run the `verify-nl-default.sh` script to check configuration
