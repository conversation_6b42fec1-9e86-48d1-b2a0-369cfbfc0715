<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICT Tilburg - Over Ons</title>
    <link rel="stylesheet" href="/assets/css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
            --primary: #2c5e92;
            --secondary: #43b380;
            --accent: #ff6b35;
            --light: #f8f9fa;
            --dark: #343a40;
            --gray: #6c757d;
            --font-main: 'Open Sans', sans-serif;
            --font-heading: 'Roboto', sans-serif;
        }
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: var(--font-main); color: var(--dark); line-height: 1.6; background-color: #f0f4f8; }
        .container { width: 90%; max-width: 1200px; margin: 0 auto; }
        header, footer { background-color: white; }
        .header-top { background-color: var(--primary); color: white; padding: 8px 0; font-size: 0.9rem; }
        .header-top .container { display: flex; justify-content: space-between; align-items: center; }
        .contact-info { display: flex; gap: 20px; }
        .contact-info a { color: white; text-decoration: none; }
        .language-selector { display: flex; gap: 10px; }
        .lang-btn { background: none; border: none; color: white; cursor: pointer; font-size: 0.9rem; opacity: 0.7; }
        .lang-btn.active { opacity: 1; font-weight: 600; text-decoration: underline; }
        .main-header { padding: 15px 0; }
        .logo-area { display: flex; align-items: center; gap: 15px; }
        .logo { width: 60px; height: 60px; background-color: var(--secondary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.8rem; }
        .logo-text { font-family: var(--font-heading); font-weight: 700; font-size: 1.8rem; color: var(--primary); }
        .logo-text span { color: var(--secondary); }
        nav ul { display: flex; list-style: none; gap: 25px; }
        nav a { text-decoration: none; color: var(--dark); font-weight: 600; font-size: 1.1rem; transition: color 0.3s; position: relative; }
        nav a:hover { color: var(--primary); }
        nav a::after { content: ''; position: absolute; bottom: -5px; left: 0; width: 0; height: 2px; background-color: var(--primary); transition: width 0.3s; }
        nav a:hover::after { width: 100%; }
        .mobile-menu-btn { display: none; background: none; border: none; font-size: 1.5rem; cursor: pointer; color: var(--primary); }
        .section { padding: 80px 0; }
        .section-title { text-align: center; margin-bottom: 50px; }
        .section-title h2 { font-size: 2.2rem; color: var(--primary); margin-bottom: 15px; position: relative; display: inline-block; }
        .section-title h2::after { content: ''; position: absolute; bottom: -10px; left: 50%; transform: translateX(-50%); width: 80px; height: 4px; background-color: var(--secondary); }
        .section-title p { color: var(--gray); max-width: 700px; margin: 20px auto 0; }
        .about-content { display: grid; grid-template-columns: 1fr 1fr; gap: 50px; align-items: center; }
        .about-text h2 { font-size: 2.2rem; color: var(--primary); margin-bottom: 20px; }
        .about-text p { margin-bottom: 20px; }
        .highlight { background-color: rgba(67, 179, 128, 0.1); padding: 20px; border-left: 4px solid var(--secondary); margin: 25px 0; }
        .about-image { border-radius: 10px; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .about-image img { width: 100%; height: auto; display: block; }
        .how-works { margin-top: 40px; }
        .steps { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px; margin-top: 30px; }
        .step { text-align: center; padding: 30px 20px; background-color: white; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.05); }
        .step-number { width: 50px; height: 50px; background-color: var(--primary); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.5rem; font-weight: 700; margin: 0 auto 20px; }
        .contact-cards { margin: 50px 0 0 0; display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px; }
        .contact-card { background-color: white; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.05); padding: 30px; display: flex; align-items: center; gap: 20px; }
        .contact-icon { width: 50px; height: 50px; background-color: rgba(44, 94, 146, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: var(--primary); font-size: 1.3rem; flex-shrink: 0; }
        .contact-details h4 { margin-bottom: 6px; font-size: 1.15rem; color: var(--primary);}
        .team-section { margin-top: 80px; }
        .team-title { text-align: center; margin-bottom: 35px; }
        .team-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); gap: 30px; }
        .team-card { background: #fff; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.05); padding: 30px 20px; text-align: center; }
        .team-avatar { width: 80px; height: 80px; border-radius: 50%; margin: 0 auto 18px; background: var(--secondary); display: flex; align-items: center; justify-content: center; color: #fff; font-size: 2.5rem;}
        .team-card h5 { font-size: 1.15rem; color: var(--primary); margin-bottom: 6px; }
        .team-card p { font-size: 0.97rem; color: var(--gray); margin-bottom: 8px;}
        footer { background-color: var(--dark); color: white; padding: 60px 0 30px; }
        .footer-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 40px; }
        .footer-col h3 { font-size: 1.3rem; margin-bottom: 20px; position: relative; padding-bottom: 10px; }
        .footer-col h3::after { content: ''; position: absolute; bottom: 0; left: 0; width: 50px; height: 2px; background-color: var(--secondary); }
        .footer-col ul { list-style: none; }
        .footer-col ul li { margin-bottom: 10px; }
        .footer-col a { color: #ddd; text-decoration: none; transition: color 0.3s; }
        .footer-col a:hover { color: var(--secondary); }
        .social-links { display: flex; gap: 15px; margin-top: 20px; }
        .social-links a { display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 50%; background-color: rgba(255,255,255,0.1); color: white; transition: all 0.3s; }
        .social-links a:hover { background-color: var(--secondary); transform: translateY(-3px); }
        .copyright { text-align: center; padding-top: 30px; border-top: 1px solid rgba(255,255,255,0.1); font-size: 0.9rem; color: #aaa; }
        @media (max-width: 992px) {
            .about-content, .contact-cards, .footer-grid, .team-grid { grid-template-columns: 1fr; }
            .about-image { order: -1; }
        }
    </style>
    <script src="/assets/js/navigation.js"></script>
    <script src="/assets/js/language.js"></script>
    <link rel="alternate" hreflang="en" href="/en/over-ons.html">
    <link rel="alternate" hreflang="pt" href="/pt/over-ons.html">
    <link rel="canonical" href="/nl/over-ons.html">
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-top">
            <div class="container">
                <div class="contact-info">
                    <a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> <EMAIL></a>
                    <a href="tel:+31612345678"><i class="fas fa-phone"></i> +31 6 1234 5678</a>
                </div>
                <div class="language-selector">
                    <button class="lang-btn active">NL</button>
                    <button class="lang-btn">PT</button>
                </div>
            </div>
        </div>
        <div class="main-header">
            <div class="container">
                <div class="logo-area">
                    <div class="logo">
                        <i class="fas fa-hands-helping"></i>
                    </div>
                    <div class="logo-text">ICT<span>Tilburg</span></div>
                </div>
                <nav>
                    <div class="logo">
                        <a href="/nl/index.html">ICT Tilburg</a>
                    </div>
                    <ul class="nav-links">
                        <li><a href="/nl/index.html">Home</a></li>
                        <li><a href="/nl/over-ons.html">Over Ons</a></li>
                        <li><a href="/nl/diensten.html">Diensten</a></li>
                        <li><a href="/nl/workshops.html">Workshops</a></li>
                        <li><a href="/nl/blog.html">Blog</a></li>
                        <li><a href="/nl/forum.html">Forum</a></li>
                        <li><a href="/nl/downloads.html">Downloads</a></li>
                        <li><a href="/nl/nieuws.html">Nieuws</a></li>
                        <li><a href="/nl/partners.html">Partners</a></li>
                        <li><a href="/nl/contact.html">Contact</a></li>
                    </ul>
                    <div class="language-selector">
                        <a href="/en/over-ons_en.html>EN</a>
                        <a href=/nl/$file class=active>NL</a>
                        <a href=/pt/$(echo "$file" | sed s/.html/_pt.html/)">PT</a>
                    </div>
                </nav>
            </div>
        </div>
    </header>

    <!-- Over Ons Sectie -->
    <section class="section about" id="sobre-nos">
        <div class="container">
            <div class="section-title">
                <h2>Over Ons</h2>
                <p>Maak kennis met het team, onze missie en hoe we werken om technologie voor iedereen in Tilburg toegankelijk te maken.</p>
            </div>
            <div class="about-content">
                <div class="about-text">
                    <h2>Uw partner voor digitale inclusie en technologische oplossingen</h2>
                    <p>
                        Wij zijn ontstaan vanuit het verlangen om <strong>IT-toegang te democratiseren</strong> en digitale inclusie te bevorderen in Tilburg en omgeving. 
                        Technologie kan voor velen een uitdaging zijn: senioren, gezinnen met een lager inkomen, kleine bedrijven, verenigingen en iedereen die wil leren of groeien.
                    </p>
                    <div class="highlight">
                        <p>
                            <b>Missie:</b> Kwalitatieve, <b>gratis</b> of toegankelijke technologische oplossingen bieden aan iedereen. 
                            Wij geloven dat <b>niemand buitengesloten mag worden</b> in het digitale tijdperk — daarom ondersteunen wij van simpele vragen tot volledige zakelijke projecten.
                        </p>
                    </div>
                    <p>
                        Wij bieden zowel remote als fysieke ondersteuning, advies, training en op maat gemaakte oplossingen met passie, ethiek en innovatie. 
                        Wij werken voornamelijk met vrije software, omdat wij geloven in de kracht van de gemeenschap en gebruikersautonomie.
                    </p>
                    <div class="how-works">
                        <h3>Hoe werkt onze ondersteuning?</h3>
                        <div class="steps">
                            <div class="step">
                                <div class="step-number">1</div>
                                <h4>Neem contact op</h4>
                                <p>Kies het kanaal dat u het prettigst vindt: e-mail, telefoon, WhatsApp of online formulier.</p>
                            </div>
                            <div class="step">
                                <div class="step-number">2</div>
                                <h4>Diagnose & Advies</h4>
                                <p>Ons team beoordeelt uw vraag en stelt de beste oplossing voor, met uitleg bij elke stap.</p>
                            </div>
                            <div class="step">
                                <div class="step-number">3</div>
                                <h4>Oplossing & Training</h4>
                                <p>Wij lossen het probleem op, trainen u indien nodig, en begeleiden tot volledige zelfstandigheid.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="about-image">
                    <img src="https://images.unsplash.com/photo-1519389950473-47ba0277781c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80" alt="ICT Tilburg Team">
                </div>
            </div>
        </div>
    </section>

    <!-- Contactvormen -->
    <section class="section" style="padding-top:40px;">
        <div class="container">
            <div class="section-title">
                <h2>Hoe kunt u ons bereiken?</h2>
                <p>We zijn altijd bereikbaar en helpen graag. Kies wat bij u past!</p>
            </div>
            <div class="contact-cards">
                <div class="contact-card">
                    <div class="contact-icon"><i class="fas fa-envelope"></i></div>
                    <div class="contact-details">
                        <h4>E-mail</h4>
                        <p>Stuur ons een e-mail met uw vraag of probleem.<br>
                        <b><EMAIL></b></p>
                    </div>
                </div>
                <div class="contact-card">
                    <div class="contact-icon"><i class="fas fa-phone"></i></div>
                    <div class="contact-details">
                        <h4>Telefoon / WhatsApp</h4>
                        <p>Bel of stuur een bericht via WhatsApp:<br>
                        <b>+31 6 1234 5678</b></p>
                    </div>
                </div>
                <div class="contact-card">
                    <div class="contact-icon"><i class="fas fa-comments"></i></div>
                    <div class="contact-details">
                        <h4>Online Chat</h4>
                        <p>Gebruik ons <a href="index.html#contact" style="color:var(--accent);text-decoration:underline;">contactformulier</a> voor een snel antwoord.</p>
                    </div>
                </div>
                <div class="contact-card">
                    <div class="contact-icon"><i class="fas fa-users"></i></div>
                    <div class="contact-details">
                        <h4>Persoonlijke afspraak</h4>
                        <p>Plan een afspraak in het Wijkcentrum Tilburg.<br>
                        <b>Heuvelring 122, 5038 CL Tilburg</b></p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Team (fictief voorbeeld) -->
    <section class="section team-section">
        <div class="container">
            <div class="team-title">
                <h2>Ons Team</h2>
                <p>Professionals met passie voor technologie, onderwijs en maatschappelijke betrokkenheid.</p>
            </div>
            <div class="team-grid">
                <div class="team-card">
                    <div class="team-avatar"><i class="fas fa-user-astronaut"></i></div>
                    <h5>João Martins</h5>
                    <p>Oprichter & Open Source Specialist</p>
                    <p>Ervaring met Linux, automatisering, seniorentraining en sociale projecten.</p>
                </div>
                <div class="team-card">
                    <div class="team-avatar"><i class="fas fa-user-nurse"></i></div>
                    <h5>Maria Oliveira</h5>
                    <p>Digitaal Adviseur & Toegankelijkheid</p>
                    <p>Maakt technologie eenvoudig voor iedereen, met empathie en geduld.</p>
                </div>
                <div class="team-card">
                    <div class="team-avatar"><i class="fas fa-user-robot"></i></div>
                    <h5>Lucas Silva</h5>
                    <p>AI & Webontwikkelaar</p>
                    <p>Bouwt slimme oplossingen en websites voor lokale bedrijven en non-profits.</p>
                </div>
                <div class="team-card">
                    <div class="team-avatar"><i class="fas fa-user-graduate"></i></div>
                    <h5>Karin van Dijk</h5>
                    <p>Trainer & Workshops</p>
                    <p>Verantwoordelijk voor de organisatie van workshops en digitale vaardigheidsevents.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <div class="logo-area">
                        <div class="logo">
                            <i class="fas fa-hands-helping"></i>
                        </div>
                        <div class="logo-text">ICT<span>Tilburg</span></div>
                    </div>
                    <p>Gratis, innovatieve IT-ondersteuning in Tilburg en omgeving.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-col">
                    <h3>Snel naar</h3>
                    <ul>
                        <li><a href="index.html#home">Home</a></li>
                        <li><a href="servicos.html">Diensten</a></li>
                        <li><a href="sobre-nos.html">Over Ons</a></li>
                        <li><a href="/nl/workshops.html">Workshops</a></li>
                        <li><a href="contacto.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Onze Diensten</h3>
                    <ul>
                        <li><a href="suporte-remoto.html">Remote Support</a></li>
                        <li><a href="open-source.html">Open Source</a></li>
                        <li><a href="gestao-negocios.html">Bedrijfsbeheer</a></li>
                        <li><a href="inteligencia-artificial.html">AI & Automatisering</a></li>
                        <li><a href="/nl/workshops.html">Workshops</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Nieuwsbrief</h3>
                    <p>Meld je aan voor nieuws, tips en uitnodigingen voor gratis workshops.</p>
                    <form>
                        <div class="form-group">
                            <input type="email" class="form-control" placeholder="Jouw e-mail" required>
                        </div>
                        <button type="submit" class="btn">Aanmelden</button>
                    </form>
                </div>
            </div>
            <div class="copyright">
                <p>&copy; 2025 ICT Tilburg. Alle rechten voorbehouden. | Technologie voor iedereen, gemeenschap op één.</p>
            </div>
        </div>
    </footer>
    <script>
        // Mobile Menu Toggle
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const navMenu = document.querySelector('nav ul');
        if (mobileMenuBtn && navMenu) {
            mobileMenuBtn.addEventListener('click', () => {
                navMenu.classList.toggle('show');
            });
        }
        // Language Switcher
        const langButtons = document.querySelectorAll('.lang-btn');
        langButtons.forEach(button => {
            button.addEventListener('click', () => {
                langButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                alert('Taal gewijzigd naar ' + button.textContent);
            });
        });
    </script>
</body>
</html>