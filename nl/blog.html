<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICT Tilburg - Blog</title>
    <link rel="stylesheet" href="/assets/css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
        body { font-family: var(--font-main); color: var(--dark); line-height: 1.6; background-color: #f0f4f8; }
        .container { width: 90%; max-width: 1200px; margin: 0 auto; }
        header, footer { background-color: white; }
        .header-top { background-color: var(--primary); color: white; padding: 8px 0; font-size: 0.9rem; }
        .header-top .container { display: flex; justify-content: space-between; align-items: center; }
        .contact-info { display: flex; gap: 20px; }
        .contact-info a { color: white; text-decoration: none; }
        .language-selector { display: flex; gap: 10px; }
        .lang-btn { background: none; border: none; color: white; cursor: pointer; font-size: 0.9rem; opacity: 0.7; }
        .lang-btn.active { opacity: 1; font-weight: 600; text-decoration: underline; }
        .main-header { padding: 15px 0; }
        .logo-area { display: flex; align-items: center; gap: 15px; }
        .logo { width: 60px; height: 60px; background-color: var(--secondary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.8rem; }
        .logo-text { font-family: var(--font-heading); font-weight: 700; font-size: 1.8rem; color: var(--primary); }
        .logo-text span { color: var(--secondary); }
        nav ul { display: flex; list-style: none; gap: 25px; }
        nav a { text-decoration: none; color: var(--dark); font-weight: 600; font-size: 1.1rem; transition: color 0.3s; position: relative; }
        nav a:hover { color: var(--primary); }
        nav a::after { content: ''; position: absolute; bottom: -5px; left: 0; width: 0; height: 2px; background-color: var(--primary); transition: width 0.3s; }
        nav a:hover::after { width: 100%; }
        .mobile-menu-btn { display: none; background: none; border: none; font-size: 1.5rem; cursor: pointer; color: var(--primary); }
        .blog-hero {
            background: linear-gradient(rgba(44, 94, 146, 0.80), rgba(44, 94, 146, 0.85)), url('https://images.unsplash.com/photo-1461749280684-dccba630e2f6?auto=format&fit=crop&w=1200&q=80') center/cover no-repeat;
            color: white;
            padding: 70px 0 50px 0;
            text-align: center;
            margin-bottom: 30px;
        }
        .blog-hero h1 { font-size: 2.4rem; margin-bottom: 14px; }
        .blog-hero p { font-size: 1.3rem; max-width: 700px; margin: 0 auto; }
        .blog-hero .hero-img {
            width: 110px;
            height: 110px;
            border-radius: 18px;
            margin: 24px auto 0 auto;
            box-shadow: 0 8px 32px rgba(44,94,146,0.10);
            border: 3px solid var(--secondary);
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .blog-hero .hero-img img {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 10px;
        }
        .blog-main { display: grid; grid-template-columns: 2fr 1fr; gap: 50px; }
        @media (max-width: 1000px) {
            .blog-main { grid-template-columns: 1fr; }
        }
        .blog-write-area {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            padding: 30px 30px 18px 30px;
            margin-bottom: 35px;
        }
        .blog-write-area h2 { color: var(--primary); margin-bottom: 18px; font-size: 1.3rem;}
        .blog-write-area input, .blog-write-area textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 14px;
            font-size: 1rem;
            font-family: var(--font-main);
        }
        .blog-write-area textarea { min-height: 100px; resize: vertical; }
        .blog-write-area button {
            background: var(--accent);
            color: #fff;
            border: none;
            padding: 11px 32px;
            border-radius: 30px;
            font-weight: bold;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.2s;
        }
        .blog-write-area button:hover { background: var(--primary);}
        .blog-articles-list { margin-bottom: 35px; }
        .blog-article {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.04);
            padding: 28px 28px 18px 28px;
            margin-bottom: 28px;
            position: relative;
            border-left: 5px solid var(--secondary);
        }
        .blog-article h3 {
            color: var(--primary);
            margin-bottom: 7px;
            font-size: 1.15rem;
        }
        .blog-article .blog-date {
            color: var(--gray);
            font-size: 0.97rem;
            margin-bottom: 13px;
        }
        .blog-article .blog-content {
            white-space: pre-wrap;
            margin-bottom: 0;
        }
        .blog-history {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 3px 12px rgba(44,94,146,0.08);
            padding: 24px;
            margin-bottom: 30px;
        }
        .blog-history h4 {
            color: var(--primary);
            margin-bottom: 12px;
        }
        .history-list {
            list-style: none;
            padding-left: 0;
        }
        .history-list li {
            margin-bottom: 12px;
            color: var(--dark);
            font-size: 1rem;
        }
        .history-list time {
            color: var(--gray);
            font-size: 0.96rem;
            margin-right: 10px;
        }
        footer { background-color: var(--dark); color: white; padding: 60px 0 30px; }
        .footer-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 40px; }
        .footer-col h3 { font-size: 1.3rem; margin-bottom: 20px; position: relative; padding-bottom: 10px; }
        .footer-col h3::after { content: ''; position: absolute; bottom: 0; left: 0; width: 50px; height: 2px; background-color: var(--secondary); }
        .footer-col ul { list-style: none; }
        .footer-col ul li { margin-bottom: 10px; }
        .footer-col a { color: #ddd; text-decoration: none; transition: color 0.3s; }
        .footer-col a:hover { color: var(--secondary); }
        .social-links { display: flex; gap: 15px; margin-top: 20px; }
        .social-links a { display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 50%; background-color: rgba(255,255,255,0.1); color: white; transition: all 0.3s; }
        .social-links a:hover { background-color: var(--secondary); transform: translateY(-3px); }
        .copyright { text-align: center; padding-top: 30px; border-top: 1px solid rgba(255,255,255,0.1); font-size: 0.9rem; color: #aaa; }
    </style>
    <script src="/assets/js/navigation.js"></script>
    <script src="/assets/js/language.js"></script>
    <link rel="alternate" hreflang="en" href="/en/blog.html">
    <link rel="alternate" hreflang="pt" href="/pt/blog.html">
    <link rel="canonical" href="/nl/blog.html">
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-top">
            <div class="container">
                <div class="contact-info">
                    <a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> <EMAIL></a>
                    <a href="tel:+31612345678"><i class="fas fa-phone"></i> +31 6 1234 5678</a>
                </div>
                <div class="language-selector">
                    <button class="lang-btn active">NL</button>
                    <button class="lang-btn">PT</button>
                </div>
            </div>
        </div>
        <div class="main-header">
            <div class="container">
                <div class="logo-area">
                    <div class="logo">
                        <i class="fas fa-hands-helping"></i>
                    </div>
                    <div class="logo-text">ICT<span>Tilburg</span></div>
                </div>
                <nav>
                <div class="logo">
                    <a href="/nl/index.html">ICT Tilburg</a>
                </div>
                <ul class="nav-links">
                    <li><a href="/nl/index.html">Home</a></li>
                    <li><a href="/nl/over-ons.html">Over Ons</a></li>
                    <li><a href="/nl/diensten.html">Diensten</a></li>
                    <li><a href="/nl/workshops.html">Workshops</a></li>
                    <li><a href="/nl/blog.html">Blog</a></li>
                    <li><a href="/nl/forum.html">Forum</a></li>
                    <li><a href="/nl/downloads.html">Downloads</a></li>
                    <li><a href="/nl/nieuws.html">Nieuws</a></li>
                    <li><a href="/nl/partners.html">Partners</a></li>
                    <li><a href="/nl/contact.html">Contact</a></li>
                </ul>
                    <div class="language-selector">
                        <a href="/en/blog_en.html>EN</a>
                        <a href=/nl/$file class=active>NL</a>
                        <a href=/pt/$(echo "$file" | sed s/.html/_pt.html/)">PT</a>
                    </div>
                </nav>
            </div>
        </div>
    </header>

    <!-- Blog Hero -->
    <section class="blog-hero">
        <div class="container">
            <div class="hero-img">
                <img src="https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=400&q=80" alt="Tech team">
            </div>
            <h1>ICT Tilburg Blog</h1>
            <p>Verhalen, ervaringen en het dagelijks leven bij ICT Tilburg. Lees hoe wij technologie inzetten voor de gemeenschap, ontdek tips en laat je inspireren door onze belevenissen!</p>
        </div>
    </section>

    <section class="section" id="blog">
        <div class="container">
            <div class="blog-main">
                <!-- Blog schrijven -->
                <div>
                    <div class="blog-write-area">
                        <h2>Schrijf een nieuw verhaal</h2>
                        <form id="blogForm">
                            <input type="text" id="blogTitle" placeholder="Titel van je verhaal" required>
                            <textarea id="blogContent" placeholder="Vertel hier je verhaal, ervaring of tip..." required></textarea>
                            <button type="submit"><i class="fas fa-plus"></i> Blog toevoegen</button>
                        </form>
                    </div>
                    <!-- Lijst met artikelen -->
                    <div id="articlesList" class="blog-articles-list"></div>
                </div>
                <!-- Blog Historiek -->
                <aside>
                    <div class="blog-history">
                        <h4>Bloghistoriek</h4>
                        <ul id="historyList" class="history-list"></ul>
                    </div>
                </aside>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <div class="logo-area">
                        <div class="logo">
                            <i class="fas fa-hands-helping"></i>
                        </div>
                        <div class="logo-text">ICT<span>Tilburg</span></div>
                    </div>
                    <p>Gratis, innovatieve IT-ondersteuning in Tilburg en omgeving.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-col">
                    <h3>Snel naar</h3>
                    <ul>
                        <li><a href="index.html#home">Home</a></li>
                        <li><a href="servicos.html">Diensten</a></li>
                        <li><a href="sobre-nos.html">Over Ons</a></li>
                        <li><a href="/nl/workshops.html">Workshops</a></li>
                        <li><a href="/nl/blog.html">Blog</a></li>
                        <li><a href="contacto.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Onze Diensten</h3>
                    <ul>
                        <li><a href="suporte-remoto.html">Remote Support</a></li>
                        <li><a href="open-source.html">Open Source</a></li>
                        <li><a href="gestao-negocios.html">Bedrijfsbeheer</a></li>
                        <li><a href="inteligencia-artificial.html">AI & Automatisering</a></li>
                        <li><a href="/nl/workshops.html">Workshops</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Nieuwsbrief</h3>
                    <p>Meld je aan voor nieuws, tips en uitnodigingen voor gratis workshops.</p>
                    <form>
                        <div class="form-group">
                            <input type="email" class="form-control" placeholder="Jouw e-mail" required>
                        </div>
                        <button type="submit" class="btn">Aanmelden</button>
                    </form>
                </div>
            </div>
            <div class="copyright">
                <p>&copy; 2025 ICT Tilburg. Alle rechten voorbehouden. | Technologie voor iedereen, gemeenschap op één.</p>
            </div>
        </div>
    </footer>
    <script>
        // Blog artikelen lokaal opslaan & tonen
        function getBlogArticles() {
            return JSON.parse(localStorage.getItem('icttilburg_blog_articles') || '[]');
        }
        function saveBlogArticles(articles) {
            localStorage.setItem('icttilburg_blog_articles', JSON.stringify(articles));
        }
        function formatDateISOtoNL(iso) {
            const d = new Date(iso);
            return d.toLocaleDateString("nl-NL", { year: 'numeric', month: 'long', day: 'numeric' });
        }
        function renderArticles() {
            const articles = getBlogArticles().sort((a, b) => new Date(b.date) - new Date(a.date));
            const articlesList = document.getElementById('articlesList');
            articlesList.innerHTML = '';
            for (const art of articles) {
                const el = document.createElement('div');
                el.className = 'blog-article';
                el.innerHTML = `
                    <h3>${art.title}</h3>
                    <div class="blog-date"><i class="fas fa-calendar-day"></i> ${formatDateISOtoNL(art.date)}</div>
                    <div class="blog-content">${art.content.replace(/</g, "&lt;").replace(/>/g, "&gt;")}</div>
                `;
                articlesList.appendChild(el);
            }
            renderHistory(articles);
        }
        function renderHistory(articles) {
            const historyList = document.getElementById('historyList');
            // Toon alleen titels en datum
            historyList.innerHTML = '';
            articles.slice().sort((a, b) => new Date(b.date) - new Date(a.date)).forEach(art => {
                const li = document.createElement('li');
                li.innerHTML = `<time>${formatDateISOtoNL(art.date)}</time> ${art.title}`;
                historyList.appendChild(li);
            });
        }
        document.getElementById('blogForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const title = document.getElementById('blogTitle').value.trim();
            const content = document.getElementById('blogContent').value.trim();
            if (title && content) {
                const articles = getBlogArticles();
                articles.push({
                    title,
                    content,
                    date: new Date().toISOString()
                });
                saveBlogArticles(articles);
                renderArticles();
                this.reset();
            }
        });
        // Initialiseer
        document.addEventListener('DOMContentLoaded', function() {
            renderArticles();
        });

        // Mobile Menu Toggle
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const navMenu = document.querySelector('nav ul');
        if (mobileMenuBtn && navMenu) {
            mobileMenuBtn.addEventListener('click', () => {
                navMenu.classList.toggle('show');
            });
        }
        // Language Switcher
        const langButtons = document.querySelectorAll('.lang-btn');
        langButtons.forEach(button => {
            button.addEventListener('click', () => {
                langButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                alert('Taal gewijzigd naar ' + button.textContent);
            });
        });
    </script>
</body>
</html>