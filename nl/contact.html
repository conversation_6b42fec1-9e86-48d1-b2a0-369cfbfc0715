<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICT Tilburg - Contact</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #2c5e92;
            --secondary: #43b380;
            --accent: #ff6b35;
            --light: #f8f9fa;
            --dark: #343a40;
            --gray: #6c757d;
            --font-main: 'Open Sans', sans-serif;
            --font-heading: 'Roboto', sans-serif;
        }
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: var(--font-main); color: var(--dark); line-height: 1.6; background-color: #f0f4f8; }
        .container { width: 90%; max-width: 1200px; margin: 0 auto; }
        header, footer { background-color: white; }
        .header-top { background-color: var(--primary); color: white; padding: 8px 0; font-size: 0.9rem; }
        .header-top .container { display: flex; justify-content: space-between; align-items: center; }
        .contact-info { display: flex; gap: 20px; }
        .contact-info a { color: white; text-decoration: none; }
        .language-selector { display: flex; gap: 10px; }
        .lang-btn { background: none; border: none; color: white; cursor: pointer; font-size: 0.9rem; opacity: 0.7; }
        .lang-btn.active { opacity: 1; font-weight: 600; text-decoration: underline; }
        .main-header { padding: 15px 0; }
        .logo-area { display: flex; align-items: center; gap: 15px; }
        .logo { width: 60px; height: 60px; background-color: var(--secondary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.8rem; }
        .logo-text { font-family: var(--font-heading); font-weight: 700; font-size: 1.8rem; color: var(--primary); }
        .logo-text span { color: var(--secondary); }
        nav ul { display: flex; list-style: none; gap: 25px; }
        nav a { text-decoration: none; color: var(--dark); font-weight: 600; font-size: 1.1rem; transition: color 0.3s; position: relative; }
        nav a:hover { color: var(--primary); }
        nav a::after { content: ''; position: absolute; bottom: -5px; left: 0; width: 0; height: 2px; background-color: var(--primary); transition: width 0.3s; }
        nav a:hover::after { width: 100%; }
        .mobile-menu-btn { display: none; background: none; border: none; font-size: 1.5rem; cursor: pointer; color: var(--primary); }
        .section { padding: 80px 0; }
        .section-title { text-align: center; margin-bottom: 50px; }
        .section-title h2 { font-size: 2.2rem; color: var(--primary); margin-bottom: 15px; position: relative; display: inline-block; }
        .section-title h2::after { content: ''; position: absolute; bottom: -10px; left: 50%; transform: translateX(-50%); width: 80px; height: 4px; background-color: var(--secondary); }
        .section-title p { color: var(--gray); max-width: 700px; margin: 20px auto 0; }
        .contact-page-grid { display: grid; grid-template-columns: 2fr 1fr; gap: 50px; }
        .contact-form { background-color: #f8f9fa; padding: 30px; border-radius: 10px; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 600; }
        .form-control { width: 100%; padding: 12px 15px; border: 1px solid #ddd; border-radius: 5px; font-family: var(--font-main); font-size: 1rem; }
        textarea.form-control { min-height: 120px; resize: vertical; }
        .btn { display: inline-block; background-color: var(--accent); color: white; padding: 12px 30px; border-radius: 50px; text-decoration: none; font-weight: 600; font-size: 1.1rem; transition: all 0.3s; border: 2px solid var(--accent); }
        .btn:hover { background-color: transparent; color: var(--accent); box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .contact-details-block { background-color: white; border-radius: 10px; padding: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.05);}
        .details-list { list-style: none; padding: 0; margin: 0 0 32px 0;}
        .details-list li { display: flex; gap: 15px; margin-bottom: 22px; align-items: flex-start;}
        .details-list .icon { width: 38px; height: 38px; border-radius: 50%; background-color: rgba(44,94,146,0.07); display: flex; align-items: center; justify-content: center; font-size: 1.2rem; color: var(--primary);}
        .contact-tools { margin-top: 25px;}
        .contact-tools-title { font-weight: bold; margin-bottom: 12px; color: var(--primary);}
        .contact-tools-list { display: flex; flex-wrap: wrap; gap: 13px;}
        .contact-tool-link { display: flex; align-items: center; gap: 8px; background: #f0f4f8; border-radius: 50px; padding: 8px 18px; text-decoration: none; color: var(--dark); font-weight: 600; transition: background 0.2s;}
        .contact-tool-link:hover { background: var(--secondary); color: white;}
        .map-section { margin-top: 40px;}
        .map-title { font-weight: 700; color: var(--primary); margin-bottom: 18px;}
        .map-frame { border: none; width: 100%; height: 260px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.07);}
        footer { background-color: var(--dark); color: white; padding: 60px 0 30px; }
        .footer-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 40px; }
        .footer-col h3 { font-size: 1.3rem; margin-bottom: 20px; position: relative; padding-bottom: 10px; }
        .footer-col h3::after { content: ''; position: absolute; bottom: 0; left: 0; width: 50px; height: 2px; background-color: var(--secondary); }
        .footer-col ul { list-style: none; }
        .footer-col ul li { margin-bottom: 10px; }
        .footer-col a { color: #ddd; text-decoration: none; transition: color 0.3s; }
        .footer-col a:hover { color: var(--secondary); }
        .social-links { display: flex; gap: 15px; margin-top: 20px; }
        .social-links a { display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 50%; background-color: rgba(255,255,255,0.1); color: white; transition: all 0.3s; }
        .social-links a:hover { background-color: var(--secondary); transform: translateY(-3px); }
        .copyright { text-align: center; padding-top: 30px; border-top: 1px solid rgba(255,255,255,0.1); font-size: 0.9rem; color: #aaa; }
        @media (max-width: 992px) {
            .contact-page-grid, .footer-grid { grid-template-columns: 1fr; }
        }
    </style>
    <script src="/assets/js/navigation.js"></script>
    <script src="/assets/js/language.js"></script>
    <link rel="alternate" hreflang="en" href="/en/contact.html">
    <link rel="alternate" hreflang="pt" href="/pt/contact.html">
    <link rel="canonical" href="/nl/contact.html">
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-top">
            <div class="container">
                <div class="contact-info" aria-label="Contact information">
                    <a href="mailto:<EMAIL>" aria-label="Email ICT Tilburg"><i class="fas fa-envelope"></i> <EMAIL></a>
                    <a href="tel:+31612345678" aria-label="Call ICT Tilburg"><i class="fas fa-phone"></i> +31 6 1234 5678</a>
                </div>
                <div class="language-selector" role="navigation" aria-label="Language selection">
                    <button class="lang-btn" aria-pressed="false" data-lang="en" data-href="../en/contact.html">EN</button>
                    <button class="lang-btn active" aria-pressed="true" data-lang="nl" data-href="contact.html">NL</button>
                    <button class="lang-btn" aria-pressed="false" data-lang="pt" data-href="../pt/contacto.html">PT</button>
                </div>
            </div>
        </div>
        <div class="main-header">
            <div class="container">
                <div class="logo-area">
                    <div class="logo">
                        <i class="fas fa-hands-helping"></i>
                    </div>
                    <div class="logo-text">ICT<span>Tilburg</span></div>
                </div>
                <nav aria-label="Main navigation">
                    <div class="logo-area">
                        <a href="index.html" class="logo-link">
                            <div class="logo" aria-label="Logo">
                                <i class="fas fa-hands-helping"></i>
                            </div>
                            <div class="logo-text">ICT<span>Tilburg</span></div>
                        </a>
                    </div>

                    <button class="mobile-menu-btn" aria-label="Open menu" aria-expanded="false">
                        <i class="fas fa-bars"></i>
                    </button>

                    <ul class="nav-links">
                        <li><a href="index.html" class="nav-item">Home</a></li>
                        <li><a href="over-ons.html" class="nav-item">Over Ons</a></li>
                        <li><a href="diensten.html" class="nav-item">Diensten</a></li>
                        <li><a href="workshops.html" class="nav-item">Workshops</a></li>
                        <li><a href="blog.html" class="nav-item">Blog & Nieuws</a></li>
                        <li><a href="forum.html" class="nav-item">Forum</a></li>
                        <li><a href="downloads.html" class="nav-item">Downloads</a></li>
                        <li><a href="partners.html" class="nav-item">Partners</a></li>
                        <li><a href="contact.html" class="nav-item">Contact</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <!-- Contact Section -->
    <section class="section" id="contact">
        <div class="container">
            <div class="section-title">
                <h2>Contact</h2>
                <p>Heeft u een vraag, technisch probleem of wilt u een afspraak maken? Neem contact op per e-mail, telefoon, bericht, chat of kom langs. Wij reageren altijd!</p>
            </div>
            <div class="contact-page-grid">
                <!-- Formulier -->
                <div>
                    <div class="contact-form">
                        <form>
                            <div class="form-group">
                                <label for="name">Volledige Naam</label>
                                <input type="text" id="name" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="email">E-mail</label>
                                <input type="email" id="email" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="subject">Onderwerp</label>
                                <input type="text" id="subject" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="message">Bericht</label>
                                <textarea id="message" class="form-control" required></textarea>
                            </div>
                            <button type="submit" class="btn">Verstuur Bericht</button>
                        </form>
                    </div>
                </div>
                <!-- Contactdetails en tools -->
                <div>
                    <div class="contact-details-block">
                        <ul class="details-list">
                            <li>
                                <span class="icon"><i class="fas fa-map-marker-alt"></i></span>
                                <span>
                                    <b>Locatie:</b><br>
                                    Wijkcentrum Tilburg<br>
                                    Heuvelring 122, 5038 CL Tilburg
                                </span>
                            </li>
                            <li>
                                <span class="icon"><i class="fas fa-envelope"></i></span>
                                <span>
                                    <b>E-mail:</b><br>
                                    <a href="mailto:<EMAIL>"><EMAIL></a><br>
                                    <a href="mailto:<EMAIL>"><EMAIL></a>
                                </span>
                            </li>
                            <li>
                                <span class="icon"><i class="fas fa-phone"></i></span>
                                <span>
                                    <b>Telefoon / WhatsApp:</b><br>
                                    <a href="tel:+31612345678">+31 6 1234 5678</a>
                                </span>
                            </li>
                        </ul>
                        <div class="contact-tools">
                            <div class="contact-tools-title">Digitale contactmogelijkheden:</div>
                            <div class="contact-tools-list">
                                <a href="https://teams.microsoft.com/" target="_blank" class="contact-tool-link"><i class="fab fa-microsoft"></i> Teams</a>
                                <a href="https://web.whatsapp.com/send?phone=31612345678" target="_blank" class="contact-tool-link"><i class="fab fa-whatsapp"></i> WhatsApp</a>
                                <a href="https://buz.chat/" target="_blank" class="contact-tool-link"><i class="fas fa-comments"></i> Buz</a>
                                <a href="https://zoom.us/" target="_blank" class="contact-tool-link"><i class="fas fa-video"></i> Zoom</a>
                                <a href="https://t.me/icttilburg" target="_blank" class="contact-tool-link"><i class="fab fa-telegram-plane"></i> Telegram</a>
                                <a href="tel:+31612345678" class="contact-tool-link"><i class="fas fa-phone"></i> Telefoon</a>
                            </div>
                        </div>
                        <div class="map-section">
                            <div class="map-title">Onze locatie:</div>
                            <iframe class="map-frame"
                                src="https://www.openstreetmap.org/export/embed.html?bbox=5.0720,51.5580,5.0850,51.5650&amp;layer=mapnik&amp;marker=51.5615,5.0785"
                                allowfullscreen="" loading="lazy"
                                referrerpolicy="no-referrer-when-downgrade"
                                title="Kaart ICT Tilburg"></iframe>
                            <div style="font-size:0.93rem;margin-top:6px;"><a href="https://www.openstreetmap.org/?mlat=51.5615&mlon=5.0785#map=17/51.5615/5.0785" target="_blank" style="color:var(--primary);text-decoration:underline;">Bekijk grote kaart</a></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <div class="logo-area">
                        <div class="logo">
                            <i class="fas fa-hands-helping"></i>
                        </div>
                        <div class="logo-text">ICT<span>Tilburg</span></div>
                    </div>
                    <p>Gratis, innovatieve IT-ondersteuning in Tilburg en omgeving.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-col">
                    <h3>Snel naar</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="over-ons.html">Over Ons</a></li>
                        <li><a href="diensten.html">Diensten</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                        <li><a href="blog.html">Blog & Nieuws</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Onze Diensten</h3>
                    <ul>
                        <li><a href="remote-support.html">Remote Support</a></li>
                        <li><a href="open-source.html">Open Source</a></li>
                        <li><a href="bedrijfsbeheer.html">Bedrijfsbeheer</a></li>
                        <li><a href="ai-automatisering.html">AI & Automatisering</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Nieuwsbrief</h3>
                    <p>Meld je aan voor nieuws, tips en uitnodigingen voor gratis workshops.</p>
                    <form>
                        <div class="form-group">
                            <input type="email" class="form-control" placeholder="Jouw e-mail" required>
                        </div>
                        <button type="submit" class="btn">Aanmelden</button>
                    </form>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="copyright">
                    <p>&copy; 2025 ICT Tilburg. Alle rechten voorbehouden. | Technologie voor iedereen, gemeenschap op één.</p>
                </div>
                <div class="footer-legal">
                    <a href="privacy.html">Privacybeleid</a>
                    <a href="terms-diensten.html">Servicevoorwaarden</a>
                    <a href="terms-gebruik.html">Gebruiksvoorwaarden</a>
                </div>
            </div>
        </div>
    </footer>
    <script>
        // Mobile Menu Toggle
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const navMenu = document.querySelector('nav ul');
        if (mobileMenuBtn && navMenu) {
            mobileMenuBtn.addEventListener('click', () => {
                navMenu.classList.toggle('show');
            });
        }
        // Language Switcher
        const langButtons = document.querySelectorAll('.lang-btn');
        langButtons.forEach(button => {
            button.addEventListener('click', () => {
                langButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                alert('Taal gewijzigd naar ' + button.textContent);
            });
        });
        // Form Submission
        const contactForm = document.querySelector('.contact-form form');
        if (contactForm) {
            contactForm.addEventListener('submit', function(e) {
                e.preventDefault();
                alert('Bedankt voor uw bericht! We nemen spoedig contact op.');
                this.reset();
            });
        }
    </script>
</body>
</html>