<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICT Tilburg - Diensten</title>
    <link rel="stylesheet" href="/assets/css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
            --primary: #2c5e92;
            --secondary: #43b380;
            --accent: #ff6b35;
            --light: #f8f9fa;
            --dark: #343a40;
            --gray: #6c757d;
            --font-main: 'Open Sans', sans-serif;
            --font-heading: 'Roboto', sans-serif;
        }
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: var(--font-main); color: var(--dark); line-height: 1.6; background-color: #f0f4f8; }
        .container { width: 90%; max-width: 1200px; margin: 0 auto; }
        header, footer { background-color: white; }
        .header-top { background-color: var(--primary); color: white; padding: 8px 0; font-size: 0.9rem; }
        .header-top .container { display: flex; justify-content: space-between; align-items: center; }
        .contact-info { display: flex; gap: 20px; }
        .contact-info a { color: white; text-decoration: none; }
        .language-selector { display: flex; gap: 10px; }
        .lang-btn { background: none; border: none; color: white; cursor: pointer; font-size: 0.9rem; opacity: 0.7; }
        .lang-btn.active { opacity: 1; font-weight: 600; text-decoration: underline; }
        .main-header { padding: 15px 0; }
        .logo-area { display: flex; align-items: center; gap: 15px; }
        .logo { width: 60px; height: 60px; background-color: var(--secondary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.8rem; }
        .logo-text { font-family: var(--font-heading); font-weight: 700; font-size: 1.8rem; color: var(--primary); }
        .logo-text span { color: var(--secondary); }
        nav ul { display: flex; list-style: none; gap: 25px; }
        nav a { text-decoration: none; color: var(--dark); font-weight: 600; font-size: 1.1rem; transition: color 0.3s; position: relative; }
        nav a:hover { color: var(--primary); }
        nav a::after { content: ''; position: absolute; bottom: -5px; left: 0; width: 0; height: 2px; background-color: var(--primary); transition: width 0.3s; }
        nav a:hover::after { width: 100%; }
        .mobile-menu-btn { display: none; background: none; border: none; font-size: 1.5rem; cursor: pointer; color: var(--primary); }
        .section { padding: 80px 0; }
        .section-title { text-align: center; margin-bottom: 50px; }
        .section-title h2 { font-size: 2.2rem; color: var(--primary); margin-bottom: 15px; position: relative; display: inline-block; }
        .section-title h2::after { content: ''; position: absolute; bottom: -10px; left: 50%; transform: translateX(-50%); width: 80px; height: 4px; background-color: var(--secondary); }
        .section-title p { color: var(--gray); max-width: 700px; margin: 20px auto 0; }
        .services-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); gap: 35px; }
        .service-card { background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0,0,0,0.05); transition: transform 0.3s; display: flex; flex-direction: column; }
        .service-card:hover { transform: translateY(-10px); }
        .service-icon { background-color: var(--primary); color: white; font-size: 2.5rem; height: 120px; display: flex; align-items: center; justify-content: center; }
        .service-content { padding: 25px; flex: 1; display: flex; flex-direction: column; }
        .service-content h3 { font-size: 1.4rem; margin-bottom: 15px; color: var(--primary); }
        .service-content ul { margin-left: 18px; margin-bottom: 15px; }
        .more-link { margin-top: auto; text-align: right; }
        .more-link a { color: var(--accent); font-weight: bold; text-decoration: none; font-size: 1rem; }
        .more-link a:hover { text-decoration: underline; }
        footer { background-color: var(--dark); color: white; padding: 60px 0 30px; }
        .footer-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 40px; }
        .footer-col h3 { font-size: 1.3rem; margin-bottom: 20px; position: relative; padding-bottom: 10px; }
        .footer-col h3::after { content: ''; position: absolute; bottom: 0; left: 0; width: 50px; height: 2px; background-color: var(--secondary); }
        .footer-col ul { list-style: none; }
        .footer-col ul li { margin-bottom: 10px; }
        .footer-col a { color: #ddd; text-decoration: none; transition: color 0.3s; }
        .footer-col a:hover { color: var(--secondary); }
        .social-links { display: flex; gap: 15px; margin-top: 20px; }
        .social-links a { display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 50%; background-color: rgba(255,255,255,0.1); color: white; transition: all 0.3s; }
        .social-links a:hover { background-color: var(--secondary); transform: translateY(-3px); }
        .copyright { text-align: center; padding-top: 30px; border-top: 1px solid rgba(255,255,255,0.1); font-size: 0.9rem; color: #aaa; }
        @media (max-width: 992px) {
            .services-grid { grid-template-columns: 1fr; }
            .footer-grid { grid-template-columns: 1fr; }
        }
    </style>
    <script src="/assets/js/navigation.js"></script>
    <script src="/assets/js/language.js"></script>
    <link rel="alternate" hreflang="en" href="/en/diensten.html">
    <link rel="alternate" hreflang="pt" href="/pt/diensten.html">
    <link rel="canonical" href="/nl/diensten.html">
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-top">
            <div class="container">
                <div class="contact-info">
                    <a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> <EMAIL></a>
                    <a href="tel:+31612345678"><i class="fas fa-phone"></i> +31 6 1234 5678</a>
                </div>
                <div class="language-selector">
                    <button class="lang-btn active">NL</button>
                    <button class="lang-btn">PT</button>
                </div>
            </div>
        </div>
        <div class="main-header">
            <div class="container">
                <div class="logo-area">
                    <div class="logo">
                        <i class="fas fa-hands-helping"></i>
                    </div>
                    <div class="logo-text">ICT<span>Tilburg</span></div>
                </div>
                <nav>
                    <div class="logo">
                        <a href="/nl/index.html">ICT Tilburg</a>
                    </div>
                    <ul class="nav-links">
                        <li><a href="/nl/index.html">Home</a></li>
                        <li><a href="/nl/over-ons.html">Over Ons</a></li>
                        <li><a href="/nl/diensten.html">Diensten</a></li>
                        <li><a href="/nl/workshops.html">Workshops</a></li>
                        <li><a href="/nl/blog.html">Blog</a></li>
                        <li><a href="/nl/forum.html">Forum</a></li>
                        <li><a href="/nl/downloads.html">Downloads</a></li>
                        <li><a href="/nl/nieuws.html">Nieuws</a></li>
                        <li><a href="/nl/partners.html">Partners</a></li>
                        <li><a href="/nl/contact.html">Contact</a></li>
                    </ul>
                    <div class="language-selector">
                        <a href="/en/diensten_en.html>EN</a>
                        <a href=/nl/$file class=active>NL</a>
                        <a href=/pt/$(echo "$file" | sed s/.html/_pt.html/)">PT</a>
                    </div>
                </nav>
            </div>
        </div>
    </header>

    <!-- Diensten Sectie -->
    <section class="section" id="servicos">
        <div class="container">
            <div class="section-title">
                <h2>Technische Diensten op Maat</h2>
                <p>Ontdek in detail hoe wij u of uw bedrijf kunnen helpen om de relatie met technologie te transformeren. Gratis, op maat én met persoonlijke begeleiding.</p>
            </div>
            <div class="services-grid">
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="service-content">
                        <h3>Multichannel Remote Support</h3>
                        <ul>
                            <li>Hulp bij technische problemen via e-mail, chat, WhatsApp of telefoon</li>
                            <li>Ondersteuning bij installatie, updates en softwareconfiguratie</li>
                            <li>Advies over toegang, netwerken en digitale veiligheid</li>
                        </ul>
                        <div class="more-link"><a href="suporte-remoto.html">Meer info &raquo;</a></div>
                    </div>
                </div>
                <div class="service-card">
                    <div class="service-icon"><i class="fab fa-linux"></i></div>
                    <div class="service-content">
                        <h3>Open Source & Linux Systemen</h3>
                        <ul>
                            <li>Installatie en maatwerk van Linux-distributies (Ubuntu, Mint, Debian, etc.)</li>
                            <li>Herstel en hergebruik van oudere computers</li>
                            <li>Training in veilige, vrije software-omgevingen</li>
                        </ul>
                        <div class="more-link"><a href="open-source.html">Meer info &raquo;</a></div>
                    </div>
                </div>
                <div class="service-card">
                    <div class="service-icon"><i class="fas fa-briefcase"></i></div>
                    <div class="service-content">
                        <h3>Commercieel, Administratief & Industrieel Beheer</h3>
                        <ul>
                            <li>Implementatie en support van open source ERP's (Odoo, ERPNext, Dolibarr)</li>
                            <li>Instellen van facturatie, voorraadbeheer en CRM</li>
                            <li>Automatisering van administratie en systeemintegratie</li>
                        </ul>
                        <div class="more-link"><a href="gestao-negocios.html">Voorbeelden &raquo;</a></div>
                    </div>
                </div>
                <div class="service-card">
                    <div class="service-icon"><i class="fas fa-brain"></i></div>
                    <div class="service-content">
                        <h3>Kunstmatige Intelligentie Ontwikkeling</h3>
                        <ul>
                            <li>Ontwikkeling van chatbots en virtuele assistenten op maat</li>
                            <li>Automatisering van processen en data-analyse</li>
                            <li>Advies bij inzet van AI in kleine bedrijven</li>
                        </ul>
                        <div class="more-link"><a href="inteligencia-artificial.html">Projecten &raquo;</a></div>
                    </div>
                </div>
                <div class="service-card">
                    <div class="service-icon"><i class="fas fa-globe"></i></div>
                    <div class="service-content">
                        <h3>Website- en Webshopontwikkeling</h3>
                        <ul>
                            <li>Moderne, toegankelijke websites op maat</li>
                            <li>Webshops met WooCommerce, Prestashop of eigen oplossingen</li>
                            <li>SEO-optimalisatie en koppeling met sociale media</li>
                        </ul>
                        <div class="more-link"><a href="websites.html">Portfolio &raquo;</a></div>
                    </div>
                </div>
                <div class="service-card">
                    <div class="service-icon"><i class="fas fa-chalkboard-teacher"></i></div>
                    <div class="service-content">
                        <h3>Workshops, Training & Advies</h3>
                        <ul>
                            <li>Workshops over digitale veiligheid, open source, automatisering en AI</li>
                            <li>Training voor groepen en individuen (beginner tot gevorderd)</li>
                            <li>Advies en begeleiding bij digitale transformatieprojecten</li>
                        </ul>
                        <div class="more-link"><a href="/nl/workshops.html">Agenda &raquo;</a></div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <div class="logo-area">
                        <div class="logo">
                            <i class="fas fa-hands-helping"></i>
                        </div>
                        <div class="logo-text">ICT<span>Tilburg</span></div>
                    </div>
                    <p>Gratis, innovatieve IT-ondersteuning in Tilburg en omgeving.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-col">
                    <h3>Snel naar</h3>
                    <ul>
                        <li><a href="index.html#home">Home</a></li>
                        <li><a href="servicos.html">Diensten</a></li>
                        <li><a href="sobre-nos.html">Over Ons</a></li>
                        <li><a href="/nl/workshops.html">Workshops</a></li>
                        <li><a href="contacto.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Onze Diensten</h3>
                    <ul>
                        <li><a href="suporte-remoto.html">Remote Support</a></li>
                        <li><a href="open-source.html">Open Source</a></li>
                        <li><a href="gestao-negocios.html">Bedrijfsbeheer</a></li>
                        <li><a href="inteligencia-artificial.html">AI & Automatisering</a></li>
                        <li><a href="/nl/workshops.html">Workshops</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Nieuwsbrief</h3>
                    <p>Meld je aan voor nieuws, tips en uitnodigingen voor gratis workshops.</p>
                    <form>
                        <div class="form-group">
                            <input type="email" class="form-control" placeholder="Jouw e-mail" required>
                        </div>
                        <button type="submit" class="btn">Aanmelden</button>
                    </form>
                </div>
            </div>
            <div class="copyright">
                <p>&copy; 2025 ICT Tilburg. Alle rechten voorbehouden. | Technologie voor iedereen, gemeenschap op één.</p>
            </div>
        </div>
    </footer>
    <script>
        // Mobile Menu Toggle
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const navMenu = document.querySelector('nav ul');
        if (mobileMenuBtn && navMenu) {
            mobileMenuBtn.addEventListener('click', () => {
                navMenu.classList.toggle('show');
            });
        }
        // Language Switcher
        const langButtons = document.querySelectorAll('.lang-btn');
        langButtons.forEach(button => {
            button.addEventListener('click', () => {
                langButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                alert('Taal gewijzigd naar ' + button.textContent);
            });
        });
    </script>
</body>
</html>