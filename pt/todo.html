<!DOCTYPE html>
<html lang="pt">
<head>
  <meta charset="UTF-8">
  <title>To-Do List App</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body { 
      font-family: 'Open Sans', sans-serif; 
      background: #f0f4f8; 
      color: #343a40; 
      margin: 0; 
      min-height: 100vh;
    }
    .container {
      max-width: 450px;
      margin: 50px auto;
      background: #fff;
      border-radius: 10px;
      box-shadow: 0 6px 30px rgba(0,0,0,0.08);
      padding: 30px 28px 24px 28px;
    }
    h1 {
      text-align: center;
      color: #2c5e92;
      margin-bottom: 24px;
    }
    form {
      display: flex;
      gap: 12px;
      margin-bottom: 24px;
    }
    input[type="text"] {
      flex: 1;
      padding: 10px 12px;
      border: 1px solid #ddd;
      border-radius: 5px;
      font-size: 1rem;
      outline: none;
    }
    button {
      background: #2c5e92;
      color: #fff;
      border: none;
      padding: 10px 20px;
      border-radius: 5px;
      font-weight: bold;
      font-size: 1rem;
      cursor: pointer;
      transition: background 0.2s;
    }
    button:hover { background: #43b380; }
    ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }
    li {
      display: flex;
      align-items: center;
      background: #f8f9fa;
      border-radius: 5px;
      margin-bottom: 10px;
      padding: 10px 12px;
      font-size: 1rem;
      transition: background 0.15s;
    }
    li.completed {
      text-decoration: line-through;
      color: #aaa;
      background: #e9ecef;
    }
    .todo-actions {
      margin-left: auto;
      display: flex;
      gap: 8px;
    }
    .todo-actions button {
      background: none;
      border: none;
      color: #2c5e92;
      padding: 5px 7px;
      font-size: 1.1rem;
      border-radius: 3px;
      cursor: pointer;
      transition: color 0.2s;
    }
    .todo-actions button:hover {
      color: #ff6b35;
      background: rgba(255,107,53,0.06);
    }
    @media (max-width: 600px) {
      .container { max-width: 98%; padding: 16px 6px 12px 6px; }
      h1 { font-size: 1.5rem; }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>To-Do List</h1>
    <form id="todo-form" autocomplete="off">
      <input type="text" id="todo-input" placeholder="Add new task..." required>
      <button type="submit">Add</button>
    </form>
    <ul id="todo-list"></ul>
  </div>
  <script>
    // Local storage key
    const STORAGE_KEY = 'todo-list';

    // DOM elements
    const form = document.getElementById('todo-form');
    const input = document.getElementById('todo-input');
    const list = document.getElementById('todo-list');

    // Load todos from localStorage or empty array
    let todos = JSON.parse(localStorage.getItem(STORAGE_KEY)) || [];

    // Render function
    function renderTodos() {
      list.innerHTML = '';
      todos.forEach((todo, idx) => {
        const li = document.createElement('li');
        if (todo.completed) li.classList.add('completed');
        li.innerHTML = `
          <span style="flex:1;cursor:pointer;" onclick="toggleTodo(${idx})">${todo.text}</span>
          <div class="todo-actions">
            <button title="Toggle Complete" onclick="toggleTodo(${idx})"><i class="fas fa-check"></i></button>
            <button title="Delete" onclick="deleteTodo(${idx})"><i class="fas fa-trash"></i></button>
          </div>
        `;
        list.appendChild(li);
      });
    }

    // Add todo
    form.addEventListener('submit', function(e) {
      e.preventDefault();
      const value = input.value.trim();
      if (value) {
        todos.push({ text: value, completed: false });
        saveAndRender();
        input.value = '';
        input.focus();
      }
    });

    // Save to localStorage and render
    function saveAndRender() {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(todos));
      renderTodos();
    }

    // Toggle complete
    window.toggleTodo = function(idx) {
      todos[idx].completed = !todos[idx].completed;
      saveAndRender();
    }

    // Delete todo
    window.deleteTodo = function(idx) {
      if (confirm('Delete this task?')) {
        todos.splice(idx, 1);
        saveAndRender();
      }
    }

    // Initial render
    renderTodos();
  </script>
  <!-- Font Awesome for icons (CDN) -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
</body>
</html>