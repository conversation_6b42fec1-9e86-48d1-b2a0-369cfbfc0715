<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICT Tilburg - Suporte Informático Gratuito & Inovador</title>
    <meta name="description" content="Suporte informático gratuito e inovador em Tilburg. Inclusão digital para todos, workshops e ajuda personalizada.">
    <link rel="icon" type="image/png" href="../favicon.png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/styles.css">

</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-top">
            <div class="container">
                <div class="contact-info" aria-label="Contact information">
                    <a href="mailto:<EMAIL>" aria-label="Email ICT Tilburg"><i class="fas fa-envelope"></i> <EMAIL></a>
                    <a href="tel:+31612345678" aria-label="Call ICT Tilburg"><i class="fas fa-phone"></i> +31 6 1234 5678</a>
                </div>
                <div class="language-selector" role="navigation" aria-label="Language selection">
                    <button class="lang-btn" aria-pressed="false" data-lang="en" data-href="../en/index.html">EN</button>
                    <button class="lang-btn" aria-pressed="false" data-lang="nl" data-href="../nl/index.html">NL</button>
                    <button class="lang-btn active" aria-pressed="true" data-lang="pt" data-href="index.html">PT</button>
                </div>
            </div>
        </div>
        
        <div class="main-header">
            <div class="container">
                <nav aria-label="Main navigation">
                    <div class="logo-area">
                        <a href="index.html" class="logo-link">
                            <div class="logo" aria-label="Logo">
                                <i class="fas fa-hands-helping"></i>
                            </div>
                            <div class="logo-text">ICT<span>Tilburg</span></div>
                        </a>
                    </div>

                    <button class="mobile-menu-btn" aria-label="Open menu" aria-expanded="false">
                        <i class="fas fa-bars"></i>
                    </button>

                    <ul class="nav-links">
                        <li><a href="index.html" class="nav-item">Início</a></li>
                        <li><a href="sobre-nos.html" class="nav-item">Sobre Nós</a></li>
                        <li><a href="servicos.html" class="nav-item">Serviços</a></li>
                        <li><a href="workshops.html" class="nav-item">Workshops</a></li>
                        <li><a href="blog.html" class="nav-item">Blog & Notícias</a></li>
                        <li><a href="forum.html" class="nav-item">Fórum</a></li>
                        <li><a href="downloads.html" class="nav-item">Downloads</a></li>
                        <li><a href="partners.html" class="nav-item">Parceiros</a></li>
                        <li><a href="contacto.html" class="nav-item">Contacto</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <section class="section" id="home">
            <div class="container">
                <div class="section-title">
                    <h1>Bem-vindo ao ICT Tilburg</h1>
                    <p>Suporte informático gratuito e especializado para a comunidade de Tilburg e arredores.</p>
                </div>
                <section class="onboarding-section" aria-label="Primeira vez aqui?">
                  <h3>É a sua primeira vez aqui? Veja como funciona:</h3>
                  <ol>
                    <li>Escolha um serviço ou workshop que se adapte às suas necessidades.</li>
                    <li>Faça sua pergunta através do <a href="contacto.html">formulário de contacto</a> ou ligue para nós.</li>
                    <li>Entraremos em contacto para planear uma solução juntos.</li>
                    <li>Desfrute de suporte informático gratuito e personalizado!</li>
                  </ol>
                  <p><b>Dica:</b> Consulte as <a href="#faq">FAQ</a> abaixo para mais respostas!</p>
                </section>
                <div class="mission-section">
                    <h2>Nossa Missão</h2>
                    <p>
                        Acreditamos que a tecnologia deve ser acessível para todos. Nossa missão é quebrar barreiras e promover a autonomia digital – para jovens e idosos, indivíduos ou empreendedores.
                    </p>
                    <a href="servicos.html" class="btn">Conheça Nossos Serviços</a>
                </div>
            </div>
        </section>
        <section class="faq-section" id="faq" aria-label="Perguntas Frequentes">
            <h2>Perguntas Frequentes (FAQ)</h2>
            <div class="faq-list">
            <details>
                <summary>Para quem é o ICT Tilburg?</summary>
                <p>Para todos em Tilburg e arredores que precisam de ajuda digital: idosos, famílias, empreendedores, associações e curiosos.</p>
            </details>
            <details>
                <summary>Os serviços são realmente gratuitos?</summary>
                <p>Sim, nossos serviços básicos são gratuitos para indivíduos, organizações sem fins lucrativos e pequenas organizações. Projetos especiais sob consulta.</p>
            </details>
            <details>
                <summary>Preciso saber muito sobre computadores?</summary>
                <p>De maneira nenhuma! Ajudamos tanto iniciantes quanto usuários avançados, passo a passo.</p>
            </details>
            <details>
                <summary>Quanto tempo demora para receber ajuda?</summary>
                <p>Normalmente respondemos em até 24 horas ao seu pedido.</p>
            </details>
            <details>
                <summary>Posso também visitar pessoalmente?</summary>
                <p>Sim, você pode marcar um horário para receber ajuda presencial em Tilburg.</p>
            </details>
        </div>
    </section>
</main>
    <!-- Footer -->
    <footer role="contentinfo">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <div class="logo-area">
                        <div class="logo" aria-label="Logo">
                            <i class="fas fa-hands-helping" aria-hidden="true"></i>
                        </div>
                        <div class="logo-text">ICT<span>Tilburg</span></div>
                    </div>
                    <p>Suporte informático gratuito, inovador e próximo da comunidade de Tilburg e região.</p>
                    <div class="social-links" aria-label="Redes sociais">
                        <a href="#" tabindex="0" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" tabindex="0" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" tabindex="0" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" tabindex="0" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-col">
                    <h3>Links Rápidos</h3>
                    <ul>
                        <li><a href="index.html">Início</a></li>
                        <li><a href="sobre-nos.html">Sobre Nós</a></li>
                        <li><a href="servicos.html">Serviços</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                        <li><a href="blog.html">Blog & Notícias</a></li>
                        <li><a href="contacto.html">Contacto</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Nossos Serviços</h3>
                    <ul>
                        <li><a href="suporte-remoto.html">Suporte Remoto</a></li>
                        <li><a href="open-source.html">Open Source</a></li>
                        <li><a href="gestao-negocios.html">Gestão para Negócios</a></li>
                        <li><a href="ia-automacao.html">IA & Automação</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Newsletter</h3>
                    <p>Receba novidades, dicas e convites para os nossos workshops gratuitos.</p>
                    <form>
                        <div class="form-group">
                            <input type="email" class="form-control" placeholder="Seu email" required>
                        </div>
                        <button type="submit" class="btn">Subscrever</button>
                    </form>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="copyright">
                    <p>&copy; 2025 ICT Tilburg. Todos os direitos reservados. | Tecnologia para todos, comunidade em primeiro lugar.</p>
                </div>
                <div class="footer-legal">
                    <a href="politica-de-privacidade.html">Política de Privacidade</a>
                    <a href="termos-de-servico.html">Termos de Serviço</a>
                    <a href="termos-de-uso.html">Termos de Uso</a>
                </div>
            </div>
        </div>
    </footer>
    <script>
        // Mobile Menu Toggle with ARIA
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const navMenu = document.querySelector('nav ul');
        if (mobileMenuBtn && navMenu) {
            mobileMenuBtn.addEventListener('click', () => {
                navMenu.classList.toggle('show');
                const expanded = mobileMenuBtn.getAttribute('aria-expanded') === 'true';
                mobileMenuBtn.setAttribute('aria-expanded', (!expanded).toString());
            });
        }
        
        // Language switcher
        const langButtons = document.querySelectorAll('.lang-btn');
        langButtons.forEach(button => {
            button.addEventListener('click', () => {
                langButtons.forEach(btn => btn.classList.remove('active'));
                langButtons.forEach(btn => btn.setAttribute('aria-pressed', 'false'));
                button.classList.add('active');
                button.setAttribute('aria-pressed', 'true');
                
                // Navigate to the appropriate page
                const href = button.getAttribute('data-href');
                if (href) {
                    window.location.href = href;
                }
            });
        });
    </script>
</body>
</html>